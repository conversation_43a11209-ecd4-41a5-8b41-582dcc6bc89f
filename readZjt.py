import logging
import os
import time
import json
import uuid
import requests
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime

# 常量配置
APP_ID = "your_app_id"
APP_SECRET = "your_app_secret"
GROUP_ID = "your_group_id"
BASE_URL = "http://41.188.137.83:10443/open/apigw"
TOKEN_CACHE_FILE = "config/app_token_cache.txt"
# MaxCount 作用是  如果聊天长记录超过MaxCount条，则只处理系统发出的信息，防止文字数量超过AI处理的长度
MaxCount = 1000

def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 文件处理器 - 每天轮转，保留7天
    file_handler = TimedRotatingFileHandler(
        os.path.join(log_dir, 'readZjt.log'),
        when='midnight',
        interval=1,
        backupCount=7
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    console_handler.setLevel(logging.INFO)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    return logger


# 初始化日志记录器
logger = setup_logger()


def get_app_token():
    """获取并缓存APP_TOKEN，使用原子文件写入确保多进程安全"""
    current_time = time.time()

    # 尝试读取缓存
    if os.path.exists(TOKEN_CACHE_FILE):
        try:
            with open(TOKEN_CACHE_FILE, "r") as f:
                data = json.load(f)
                cache_time = data.get("cache_time", 0)
                token = data.get("app_token", "")

                # 检查Token是否在有效期内（提前5分钟刷新）
                if token and current_time < cache_time:
                    logger.info("使用缓存的app_token")
                    return token
        except (json.JSONDecodeError, KeyError, IOError) as e:
            logger.warning(f"读取token缓存失败: {e}")

    # 获取新Token
    logger.info("正在获取新的app_token")
    url = f"{BASE_URL}/v1/apptoken/create?grant_type=client_credential&appid={APP_ID}&secret={APP_SECRET}"
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        result = response.json()

        if result.get("errCode") != 0:
            raise Exception(f"Token获取失败: {result.get('errMsg')}")

        app_token = result["data"]["appToken"]
        expires_in = result["data"]["expiresIn"]

        # 计算缓存过期时间（当前时间 + 有效期 - 300秒缓冲）
        cache_time = current_time + expires_in - 300

        # 准备缓存数据
        cache_data = {
            "app_token": app_token,
            "cache_time": cache_time
        }

        # 原子写入：先写入临时文件再重命名
        temp_file = f"{TOKEN_CACHE_FILE}.{uuid.uuid4().hex}.tmp"
        try:
            # 写入临时文件
            with open(temp_file, "w") as f:
                json.dump(cache_data, f)

            # 原子重命名
            # Unix-like: 重命名是原子操作
            os.rename(temp_file, TOKEN_CACHE_FILE)

            logger.info("新的app_token已缓存")
        finally:
            # 清理可能的临时文件残留
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass

        return app_token

    except requests.RequestException as e:
        logger.error(f"网络请求失败: {str(e)}")
        raise Exception(f"网络请求失败: {str(e)}")


def fetch_group_messages():
    """获取并过滤群聊记录"""
    logger.info(f"开始获取群组 {GROUP_ID} 的消息")
    app_token = get_app_token()
    url = f"{BASE_URL}/v1/zjga/police/group/msg/fetch?app_token={app_token}"

    # 时间范围（从1970年至今）
    end_timestamp = int(time.time() * 1000)  # 当前毫秒级时间戳
    text_media_messages = []
    page = 1
    page_size = 100  # 每页数量

    while True:
        payload = {
            "page": page,
            "count": page_size,
            "groupId": GROUP_ID,
            "beginEventDate": "0",  # 从最早开始
            "endEventDate": str(end_timestamp),
            "sortOrder": "asc"  # 按时间升序获取
        }

        try:
            logger.debug(f"获取第 {page} 页消息")
            response = requests.post(url, json=payload, timeout=15)
            response.raise_for_status()
            result = response.json()

            if result.get("errCode") != 0:
                logger.error(f"消息获取失败: {result.get('errMsg')}")
                raise Exception(f"消息获取失败: {result.get('errMsg')}")

            messages = result.get("data", [])
            total = result.get("total", 0)
            if not messages:
                logger.info("已获取所有消息")
                break  # 无更多数据

            # 过滤TextMedia消息
            for msg in messages:
                if msg.get("msgType") == "TextMedia" and (total < MaxCount or msg.get("sendStaffType") != 'staff'):
                    # 添加必要字段并存储
                    filtered_msg = {
                        "MsgId": msg.get("MsgId"),
                        "Text": msg.get("Text", ""),
                        "SendName": msg.get("SendName", ""),
                        "SendStaffId": msg.get("SendStaffId", ""),
                        "Timestamp": msg.get("CreateTime", 0)
                    }
                    text_media_messages.append(filtered_msg)

            # 更新进度
            logger.info(f"已获取第 {page} 页, 当前总数: {len(text_media_messages)}")
            page += 1

            # 简单限流
            time.sleep(0.01)

        except requests.RequestException as e:
            logger.error(f"请求异常: {str(e)}, 重试中...")
            time.sleep(1)

    logger.info(f"共获取到 {len(text_media_messages)} 条TextMedia消息")
    return text_media_messages


def main():
    """主执行函数"""
    start_time = time.time()
    logger.info("程序开始执行")

    try:
        messages = fetch_group_messages()
        output_file = f"text_media_messages_{int(time.time())}.json"

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(messages, f, ensure_ascii=False, indent=2)

        logger.info(f"结果已保存至: {output_file}")
        print(f"成功获取 {len(messages)} 条TextMedia消息")
        print(f"结果已保存至: {output_file}")

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"程序执行失败: {str(e)}")

    elapsed_time = time.time() - start_time
    logger.info(f"程序执行完成，总耗时: {elapsed_time:.2f}秒")
    print(f"总耗时: {elapsed_time:.2f}秒")


if __name__ == "__main__":
    main()
