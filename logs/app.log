2025-08-02 20:18:33,106 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:33,108 - root - INFO - 配置加载完成
2025-08-02 20:18:33,108 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:18:33,109 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:18:33,686 - root - INFO - 启动工作进程 #1 (PID: 75463)
2025-08-02 20:18:33,692 - root - INFO - 启动工作进程 #2 (PID: 75464)
2025-08-02 20:18:33,700 - root - INFO - 启动工作进程 #3 (PID: 75465)
2025-08-02 20:18:33,712 - root - INFO - 启动工作进程 #4 (PID: 75466)
2025-08-02 20:18:34,655 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:34,656 - root - INFO - 配置加载完成
2025-08-02 20:18:34,660 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:34,662 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:34,663 - root - INFO - 应用路由注册完成
2025-08-02 20:18:34,666 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:34,667 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:34,680 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:34,681 - root - INFO - 配置加载完成
2025-08-02 20:18:34,687 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:34,688 - root - INFO - 配置加载完成
2025-08-02 20:18:34,695 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:34,695 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:34,696 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:34,696 - root - INFO - 配置加载完成
2025-08-02 20:18:34,697 - root - INFO - 应用路由注册完成
2025-08-02 20:18:34,697 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:34,698 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:34,699 - root - INFO - 应用路由注册完成
2025-08-02 20:18:34,699 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:34,699 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:34,701 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:34,702 - root - INFO - 应用路由注册完成
2025-08-02 20:18:34,703 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:34,703 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:34,701 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:34,704 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:34,713 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:34,823 - root - INFO - 应用已关闭
2025-08-02 20:18:45,348 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:45,349 - root - INFO - 配置加载完成
2025-08-02 20:18:45,349 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:18:45,349 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:18:45,362 - root - INFO - 启动工作进程 #1 (PID: 75476)
2025-08-02 20:18:45,366 - root - INFO - 启动工作进程 #2 (PID: 75477)
2025-08-02 20:18:45,375 - root - INFO - 启动工作进程 #3 (PID: 75478)
2025-08-02 20:18:45,384 - root - INFO - 启动工作进程 #4 (PID: 75479)
2025-08-02 20:18:46,013 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:46,014 - root - INFO - 配置加载完成
2025-08-02 20:18:46,017 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:46,018 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:46,018 - root - INFO - 应用路由注册完成
2025-08-02 20:18:46,021 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:46,022 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:46,033 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:46,034 - root - INFO - 配置加载完成
2025-08-02 20:18:46,036 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:46,037 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:46,038 - root - INFO - 应用路由注册完成
2025-08-02 20:18:46,038 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:46,039 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:46,039 - root - INFO - 配置加载完成
2025-08-02 20:18:46,043 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:46,040 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:46,043 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:46,045 - root - INFO - 应用路由注册完成
2025-08-02 20:18:46,046 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:46,047 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:46,082 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:18:46,083 - root - INFO - 配置加载完成
2025-08-02 20:18:46,085 - root - INFO - MySQL连接池处理器已注册
2025-08-02 20:18:46,086 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:18:46,087 - root - INFO - 应用路由注册完成
2025-08-02 20:18:46,087 - root - INFO - 初始化MySQL连接池...
2025-08-02 20:18:46,090 - root - CRITICAL - MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '127.0.0.1'")
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 527, in _connect
    self._reader, self._writer = await \
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/tasks.py", line 408, in wait_for
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 88, in _open_connection
    transport, _ = await loop.create_connection(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1064, in create_connection
    raise exceptions[0]
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 1049, in create_connection
    sock = await self._connect_sock(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/base_events.py", line 960, in _connect_sock
    await self.sock_connect(sock, address)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 499, in sock_connect
    return await fut
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/asyncio/selector_events.py", line 534, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 61] Connect call failed ('127.0.0.1', 3306)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2/main.py", line 492, in init_mysql_pool
    app['mysql_pool'] = await aiomysql.create_pool(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 29, in _create_pool
    await pool._fill_free_pool(False)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/pool.py", line 182, in _fill_free_pool
    conn = await connect(echo=self._echo, loop=self._loop,
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 75, in _connect
    await conn._connect()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/aiomysql/connection.py", line 563, in _connect
    raise OperationalError(
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1'")
2025-08-02 20:18:46,168 - root - INFO - 应用已关闭
2025-08-02 20:19:56,619 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:19:56,619 - root - INFO - 配置加载完成
2025-08-02 20:19:56,620 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:19:56,620 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:19:56,632 - root - INFO - 启动工作进程 #1 (PID: 75522)
2025-08-02 20:19:56,637 - root - INFO - 启动工作进程 #2 (PID: 75523)
2025-08-02 20:19:56,643 - root - INFO - 启动工作进程 #3 (PID: 75524)
2025-08-02 20:19:56,652 - root - INFO - 启动工作进程 #4 (PID: 75525)
2025-08-02 20:19:57,381 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:19:57,381 - root - INFO - 配置加载完成
2025-08-02 20:19:57,388 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:19:57,388 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:19:57,388 - root - INFO - 配置加载完成
2025-08-02 20:19:57,389 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:19:57,390 - root - INFO - 应用路由注册完成
2025-08-02 20:19:57,391 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:19:57,392 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:19:57,392 - root - INFO - 应用路由注册完成
2025-08-02 20:19:57,392 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 20:19:57,395 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 20:19:57,406 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:19:57,407 - root - INFO - 配置加载完成
2025-08-02 20:19:57,410 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:19:57,411 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:19:57,412 - root - INFO - 应用路由注册完成
2025-08-02 20:19:57,413 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 20:19:57,417 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:19:57,418 - root - INFO - 配置加载完成
2025-08-02 20:19:57,423 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:19:57,423 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:19:57,424 - root - INFO - 应用路由注册完成
2025-08-02 20:19:57,429 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 20:23:04,381 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:04 +0800] "GET /h5/index.html HTTP/1.1" 200 237 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:04,764 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:04 +0800] "GET /h5/static/index.883130ca.css HTTP/1.1" 304 181 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:04,765 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:04 +0800] "GET /h5/static/js/chunk-vendors.87fb28f4.js HTTP/1.1" 304 181 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:04,768 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:04 +0800] "GET /h5/static/js/index.c34930b7.js HTTP/1.1" 304 181 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:06,501 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:06 +0800] "GET /h5/static/js/pages-index-index.02615edc.js HTTP/1.1" 304 181 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:06,683 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:23:06,688 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:06 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 841 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:06,746 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:06 +0800] "GET /h5/static/webfonts/fa-solid-900.woff2 HTTP/1.1" 304 181 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:23:06,958 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:23:06 +0800] "GET /favicon.ico HTTP/1.1" 404 175 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:24:05,171 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/index.html HTTP/1.1" 200 237 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,255 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/static/index.883130ca.css HTTP/1.1" 200 240 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,273 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/static/js/chunk-vendors.87fb28f4.js HTTP/1.1" 200 255 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,282 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/static/js/index.c34930b7.js HTTP/1.1" 200 255 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,801 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/static/js/pages-index-index.02615edc.js HTTP/1.1" 200 254 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,926 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:24:05,927 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 841 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:05,985 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:05 +0800] "GET /h5/static/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 257 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:06,947 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:06 +0800] "GET /h5/static/webfonts/fa-solid-900.ttf HTTP/1.1" 200 257 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:06,968 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:06 +0800] "GET /favicon.ico HTTP/1.1" 404 175 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:07,125 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:07 +0800] "GET /h5/static/webfonts/fa-regular-400.woff2 HTTP/1.1" 200 255 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:24:07,202 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:24:07 +0800] "GET /h5/static/webfonts/fa-regular-400.ttf HTTP/1.1" 200 255 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:25:08,102 - root - INFO - 收到completions请求: 编写首报...
2025-08-02 20:25:08,107 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:25:10,180 - root - INFO - AI模型响应状态: 200
2025-08-02 20:25:29,459 - root - INFO - 发送了 2592 个数据块给客户端
2025-08-02 20:25:29,461 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:25:08 +0800] "POST /v1/chat/completions HTTP/1.1" 200 301412 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:25:48,610 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 20:25:48,612 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 20:25:48,619 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 20:25:48,621 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 20:25:48,632 - root - INFO - 应用已关闭
2025-08-02 20:26:45,455 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:26:45,456 - root - INFO - 配置加载完成
2025-08-02 20:26:45,456 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:26:45,456 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:26:45,469 - root - INFO - 启动工作进程 #1 (PID: 75864)
2025-08-02 20:26:45,474 - root - INFO - 启动工作进程 #2 (PID: 75865)
2025-08-02 20:26:45,480 - root - INFO - 启动工作进程 #3 (PID: 75866)
2025-08-02 20:26:45,487 - root - INFO - 启动工作进程 #4 (PID: 75867)
2025-08-02 20:26:46,243 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:26:46,244 - root - INFO - 配置加载完成
2025-08-02 20:26:46,251 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:26:46,251 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:26:46,252 - root - INFO - 应用路由注册完成
2025-08-02 20:26:46,259 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:26:46,259 - root - INFO - 配置加载完成
2025-08-02 20:26:46,260 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:26:46,261 - root - INFO - 配置加载完成
2025-08-02 20:26:46,261 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 20:26:46,262 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:26:46,263 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:26:46,264 - root - INFO - 应用路由注册完成
2025-08-02 20:26:46,264 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:26:46,265 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:26:46,266 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 20:26:46,267 - root - INFO - 应用路由注册完成
2025-08-02 20:26:46,268 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 20:26:46,283 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:26:46,284 - root - INFO - 配置加载完成
2025-08-02 20:26:46,292 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:26:46,293 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:26:46,293 - root - INFO - 应用路由注册完成
2025-08-02 20:26:46,294 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 20:26:53,535 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:26:53 +0800] "GET /h5/index.html HTTP/1.1" 200 237 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:26:53,742 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:26:53,744 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:26:53 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:27:00,785 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:27:00,787 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:27:03,678 - root - INFO - AI模型响应状态: 200
2025-08-02 20:27:30,332 - root - INFO - 发送了 3604 个数据块给客户端
2025-08-02 20:27:30,335 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:27:00 +0800] "POST /v1/chat/completions HTTP/1.1" 200 418553 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:32:31,683 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:32:31,684 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:32:32,524 - root - INFO - AI模型响应状态: 200
2025-08-02 20:32:59,459 - root - INFO - 发送了 3642 个数据块给客户端
2025-08-02 20:32:59,462 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:32:31 +0800] "POST /v1/chat/completions HTTP/1.1" 200 422732 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
2025-08-02 20:33:38,167 - root - INFO - 收到completions请求: 编写首报...
2025-08-02 20:33:38,195 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:33:42,117 - root - INFO - AI模型响应状态: 200
2025-08-02 20:34:07,196 - root - INFO - 发送了 3386 个数据块给客户端
2025-08-02 20:34:07,197 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:33:38 +0800] "POST /v1/chat/completions HTTP/1.1" 200 393387 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:34:27,552 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:34:27 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:34:29,545 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:34:29,547 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:34:29 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:34:35,308 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:34:35,312 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:34:36,125 - root - INFO - AI模型响应状态: 200
2025-08-02 20:35:03,325 - root - INFO - 发送了 3678 个数据块给客户端
2025-08-02 20:35:03,328 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:34:35 +0800] "POST /v1/chat/completions HTTP/1.1" 200 426893 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:37:34,957 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:37:34 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:37:37,047 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:37:37,050 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:37:37 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:37:41,301 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:37:41,307 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:37:43,012 - root - INFO - AI模型响应状态: 200
2025-08-02 20:38:10,876 - root - INFO - 发送了 3778 个数据块给客户端
2025-08-02 20:38:10,888 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:37:41 +0800] "POST /v1/chat/completions HTTP/1.1" 200 438347 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:38:34,521 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:38:34,523 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:38:35,268 - root - INFO - AI模型响应状态: 200
2025-08-02 20:39:00,577 - root - INFO - 发送了 3592 个数据块给客户端
2025-08-02 20:39:00,578 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:38:33 +0800] "POST /v1/chat/completions HTTP/1.1" 200 416874 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:40:50,465 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 20:40:50,467 - worker-0 - INFO - 工作进程 #0 已关闭
2025-08-02 20:40:50,468 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 20:40:50,468 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 20:40:50,479 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 20:40:50,505 - root - INFO - 应用已关闭
2025-08-02 20:40:53,570 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:40:53,571 - root - INFO - 配置加载完成
2025-08-02 20:40:53,571 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:40:53,571 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:40:53,582 - root - INFO - 启动工作进程 #1 (PID: 76348)
2025-08-02 20:40:53,587 - root - INFO - 启动工作进程 #2 (PID: 76349)
2025-08-02 20:40:53,591 - root - INFO - 启动工作进程 #3 (PID: 76350)
2025-08-02 20:40:53,598 - root - INFO - 启动工作进程 #4 (PID: 76351)
2025-08-02 20:40:54,415 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:40:54,415 - root - INFO - 配置加载完成
2025-08-02 20:40:54,418 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:40:54,418 - root - INFO - 配置加载完成
2025-08-02 20:40:54,418 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:40:54,419 - root - INFO - 配置加载完成
2025-08-02 20:40:54,421 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:40:54,422 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:40:54,422 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:40:54,423 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:40:54,423 - root - INFO - 应用路由注册完成
2025-08-02 20:40:54,423 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:40:54,423 - root - INFO - 应用路由注册完成
2025-08-02 20:40:54,424 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:40:54,424 - root - INFO - 应用路由注册完成
2025-08-02 20:40:54,425 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 20:40:54,425 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 20:40:54,426 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 20:40:54,428 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:40:54,429 - root - INFO - 配置加载完成
2025-08-02 20:40:54,431 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:40:54,431 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:40:54,432 - root - INFO - 应用路由注册完成
2025-08-02 20:40:54,433 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 20:40:56,732 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:40:56 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:40:59,132 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:40:59,134 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:40:59 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:41:03,390 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:41:03,392 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:41:05,321 - root - INFO - AI模型响应状态: 200
2025-08-02 20:41:38,384 - root - INFO - 发送了 4100 个数据块给客户端
2025-08-02 20:41:38,385 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:41:03 +0800] "POST /v1/chat/completions HTTP/1.1" 200 475900 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:46:35,289 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 20:46:35,289 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 20:46:35,289 - worker-0 - INFO - 工作进程 #0 已关闭
2025-08-02 20:46:35,290 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 20:46:35,290 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 20:46:35,316 - root - INFO - 应用已关闭
2025-08-02 20:46:39,624 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:46:39,625 - root - INFO - 配置加载完成
2025-08-02 20:46:39,625 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 20:46:39,625 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 20:46:39,634 - root - INFO - 启动工作进程 #1 (PID: 76583)
2025-08-02 20:46:39,637 - root - INFO - 启动工作进程 #2 (PID: 76584)
2025-08-02 20:46:39,641 - root - INFO - 启动工作进程 #3 (PID: 76585)
2025-08-02 20:46:39,653 - root - INFO - 启动工作进程 #4 (PID: 76586)
2025-08-02 20:46:40,200 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:46:40,201 - root - INFO - 配置加载完成
2025-08-02 20:46:40,203 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:46:40,204 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:46:40,204 - root - INFO - 应用路由注册完成
2025-08-02 20:46:40,206 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:46:40,207 - root - INFO - 配置加载完成
2025-08-02 20:46:40,208 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 20:46:40,209 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:46:40,210 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:46:40,211 - root - INFO - 应用路由注册完成
2025-08-02 20:46:40,213 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 20:46:40,218 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:46:40,219 - root - INFO - 配置加载完成
2025-08-02 20:46:40,220 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:46:40,221 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:46:40,221 - root - INFO - 应用路由注册完成
2025-08-02 20:46:40,222 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 20:46:40,229 - root - INFO - 加载配置文件: config/config.json
2025-08-02 20:46:40,229 - root - INFO - 配置加载完成
2025-08-02 20:46:40,230 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 20:46:40,231 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 20:46:40,231 - root - INFO - 应用路由注册完成
2025-08-02 20:46:40,231 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 20:46:43,654 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:46:43 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:46:44,880 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 20:46:44,880 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:46:44 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:47:13,050 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:47:13,055 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:47:13,900 - root - INFO - AI模型响应状态: 200
2025-08-02 20:47:40,051 - root - INFO - 发送了 3516 个数据块给客户端
2025-08-02 20:47:40,055 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:47:13 +0800] "POST /v1/chat/completions HTTP/1.1" 200 408458 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:48:59,399 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:48:59,400 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:49:03,340 - root - INFO - AI模型响应状态: 200
2025-08-02 20:49:30,715 - root - INFO - 发送了 3700 个数据块给客户端
2025-08-02 20:49:30,717 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:48:59 +0800] "POST /v1/chat/completions HTTP/1.1" 200 429601 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:50:28,792 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:50:28,817 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:50:29,883 - root - INFO - AI模型响应状态: 200
2025-08-02 20:50:53,892 - root - INFO - 发送了 3220 个数据块给客户端
2025-08-02 20:50:53,893 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:50:28 +0800] "POST /v1/chat/completions HTTP/1.1" 200 373743 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:52:41,064 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:52:41,068 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:52:41,934 - root - INFO - AI模型响应状态: 200
2025-08-02 20:53:06,553 - root - INFO - 发送了 3290 个数据块给客户端
2025-08-02 20:53:06,554 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:52:41 +0800] "POST /v1/chat/completions HTTP/1.1" 200 382241 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:55:08,773 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:55:08,776 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:55:10,563 - root - INFO - AI模型响应状态: 200
2025-08-02 20:55:33,589 - root - INFO - 发送了 3120 个数据块给客户端
2025-08-02 20:55:33,592 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:55:08 +0800] "POST /v1/chat/completions HTTP/1.1" 200 362363 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 20:56:31,648 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 20:56:31,650 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 20:56:32,794 - root - INFO - AI模型响应状态: 200
2025-08-02 20:56:56,204 - root - INFO - 发送了 3176 个数据块给客户端
2025-08-02 20:56:56,205 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:20:56:31 +0800] "POST /v1/chat/completions HTTP/1.1" 200 369077 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:00:07,404 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:00:07,408 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:00:08,660 - root - INFO - AI模型响应状态: 200
2025-08-02 21:00:33,749 - root - INFO - 发送了 3382 个数据块给客户端
2025-08-02 21:00:33,750 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:00:07 +0800] "POST /v1/chat/completions HTTP/1.1" 200 392698 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:00:55,235 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:00:55,236 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:00:59,145 - root - INFO - AI模型响应状态: 200
2025-08-02 21:01:25,163 - root - INFO - 发送了 3526 个数据块给客户端
2025-08-02 21:01:25,163 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:00:55 +0800] "POST /v1/chat/completions HTTP/1.1" 200 409464 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:03:09,329 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:03:09,339 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:03:10,528 - root - INFO - AI模型响应状态: 200
2025-08-02 21:03:37,593 - root - INFO - 发送了 3656 个数据块给客户端
2025-08-02 21:03:37,594 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:03:09 +0800] "POST /v1/chat/completions HTTP/1.1" 200 424642 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:07:50,693 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 21:07:50,693 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 21:07:50,694 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 21:07:50,704 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 21:07:50,714 - root - INFO - 应用已关闭
2025-08-02 21:07:53,059 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:07:53,060 - root - INFO - 配置加载完成
2025-08-02 21:07:53,060 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 21:07:53,060 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 21:07:53,068 - root - INFO - 启动工作进程 #1 (PID: 77345)
2025-08-02 21:07:53,072 - root - INFO - 启动工作进程 #2 (PID: 77346)
2025-08-02 21:07:53,076 - root - INFO - 启动工作进程 #3 (PID: 77347)
2025-08-02 21:07:53,081 - root - INFO - 启动工作进程 #4 (PID: 77348)
2025-08-02 21:07:53,555 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:07:53,556 - root - INFO - 配置加载完成
2025-08-02 21:07:53,569 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:07:53,570 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:07:53,571 - root - INFO - 应用路由注册完成
2025-08-02 21:07:53,578 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 21:07:53,587 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:07:53,588 - root - INFO - 配置加载完成
2025-08-02 21:07:53,588 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:07:53,588 - root - INFO - 配置加载完成
2025-08-02 21:07:53,590 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:07:53,590 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:07:53,591 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:07:53,591 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:07:53,592 - root - INFO - 应用路由注册完成
2025-08-02 21:07:53,592 - root - INFO - 应用路由注册完成
2025-08-02 21:07:53,593 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:07:53,593 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 21:07:53,593 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 21:07:53,593 - root - INFO - 配置加载完成
2025-08-02 21:07:53,595 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:07:53,596 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:07:53,596 - root - INFO - 应用路由注册完成
2025-08-02 21:07:53,597 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 21:07:56,450 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:07:56 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:07:57,906 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 21:07:57,907 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:07:57 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:08:02,009 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:08:02,011 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:08:02,888 - root - INFO - AI模型响应状态: 200
2025-08-02 21:08:33,147 - root - INFO - 发送了 4100 个数据块给客户端
2025-08-02 21:08:33,151 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:08:02 +0800] "POST /v1/chat/completions HTTP/1.1" 200 476299 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:08:45,305 - root - INFO - 收到completions请求: 是否上报...
2025-08-02 21:08:45,309 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:08:46,102 - root - INFO - AI模型响应状态: 200
2025-08-02 21:09:01,091 - root - INFO - 发送了 2062 个数据块给客户端
2025-08-02 21:09:01,092 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:08:45 +0800] "POST /v1/chat/completions HTTP/1.1" 200 240194 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:09:33,417 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:09:33,418 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:09:34,539 - root - INFO - AI模型响应状态: 200
2025-08-02 21:09:59,704 - root - INFO - 发送了 3414 个数据块给客户端
2025-08-02 21:09:59,705 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:09:33 +0800] "POST /v1/chat/completions HTTP/1.1" 200 396321 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:10:02,406 - root - INFO - 收到completions请求: 是否上报...
2025-08-02 21:10:02,408 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:10:04,132 - root - INFO - AI模型响应状态: 200
2025-08-02 21:10:15,868 - root - INFO - 发送了 1612 个数据块给客户端
2025-08-02 21:10:15,869 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:10:02 +0800] "POST /v1/chat/completions HTTP/1.1" 200 187919 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:10:36,273 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:10:36,275 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:10:37,137 - root - INFO - AI模型响应状态: 200
2025-08-02 21:11:05,575 - root - INFO - 发送了 3864 个数据块给客户端
2025-08-02 21:11:05,577 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:10:36 +0800] "POST /v1/chat/completions HTTP/1.1" 200 448576 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:12:45,051 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:12:45,056 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:12:46,098 - root - INFO - AI模型响应状态: 200
2025-08-02 21:13:14,419 - root - INFO - 发送了 3584 个数据块给客户端
2025-08-02 21:13:14,421 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:12:45 +0800] "POST /v1/chat/completions HTTP/1.1" 200 416241 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:14:22,879 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:14:22,883 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:14:23,929 - root - INFO - AI模型响应状态: 200
2025-08-02 21:14:49,247 - root - INFO - 发送了 3432 个数据块给客户端
2025-08-02 21:14:49,249 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:14:22 +0800] "POST /v1/chat/completions HTTP/1.1" 200 398694 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:15:33,451 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:15:33,454 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:15:34,236 - root - INFO - AI模型响应状态: 200
2025-08-02 21:15:58,941 - root - INFO - 发送了 3336 个数据块给客户端
2025-08-02 21:15:58,942 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:15:33 +0800] "POST /v1/chat/completions HTTP/1.1" 200 387279 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:17:55,997 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:17:55,999 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:17:57,763 - root - INFO - AI模型响应状态: 200
2025-08-02 21:18:21,213 - root - INFO - 发送了 3162 个数据块给客户端
2025-08-02 21:18:21,217 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:17:55 +0800] "POST /v1/chat/completions HTTP/1.1" 200 367068 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:18:56,346 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:18:56,350 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:18:59,187 - root - INFO - AI模型响应状态: 200
2025-08-02 21:19:28,365 - root - INFO - 发送了 3960 个数据块给客户端
2025-08-02 21:19:28,367 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:18:56 +0800] "POST /v1/chat/completions HTTP/1.1" 200 460009 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:20:10,333 - root - INFO - 收到completions请求: 是否上报...
2025-08-02 21:20:10,334 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:20:12,075 - root - INFO - AI模型响应状态: 200
2025-08-02 21:20:25,369 - root - INFO - 发送了 1822 个数据块给客户端
2025-08-02 21:20:25,370 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:20:10 +0800] "POST /v1/chat/completions HTTP/1.1" 200 212301 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:20:59,203 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:20:59,207 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:21:01,149 - root - INFO - AI模型响应状态: 200
2025-08-02 21:21:30,239 - root - INFO - 发送了 3928 个数据块给客户端
2025-08-02 21:21:30,240 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:20:59 +0800] "POST /v1/chat/completions HTTP/1.1" 200 455862 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:24:04,652 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:24:04,654 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:24:05,487 - root - INFO - AI模型响应状态: 200
2025-08-02 21:24:35,770 - root - INFO - 发送了 4100 个数据块给客户端
2025-08-02 21:24:35,772 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:24:04 +0800] "POST /v1/chat/completions HTTP/1.1" 200 475764 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:26:08,112 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:26:08,113 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:26:09,996 - root - INFO - AI模型响应状态: 200
2025-08-02 21:26:38,734 - root - INFO - 发送了 3890 个数据块给客户端
2025-08-02 21:26:38,744 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:26:08 +0800] "POST /v1/chat/completions HTTP/1.1" 200 451462 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:27:09,715 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 21:27:09,715 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 21:27:09,716 - worker-0 - INFO - 工作进程 #0 已关闭
2025-08-02 21:27:09,724 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 21:27:09,731 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 21:27:09,736 - root - INFO - 应用已关闭
2025-08-02 21:27:12,614 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:27:12,615 - root - INFO - 配置加载完成
2025-08-02 21:27:12,615 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 21:27:12,615 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 21:27:12,624 - root - INFO - 启动工作进程 #1 (PID: 78036)
2025-08-02 21:27:12,627 - root - INFO - 启动工作进程 #2 (PID: 78037)
2025-08-02 21:27:12,630 - root - INFO - 启动工作进程 #3 (PID: 78038)
2025-08-02 21:27:12,636 - root - INFO - 启动工作进程 #4 (PID: 78039)
2025-08-02 21:27:13,088 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:27:13,088 - root - INFO - 配置加载完成
2025-08-02 21:27:13,089 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:27:13,089 - root - INFO - 配置加载完成
2025-08-02 21:27:13,090 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:27:13,091 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:27:13,091 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:27:13,091 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:27:13,092 - root - INFO - 应用路由注册完成
2025-08-02 21:27:13,092 - root - INFO - 应用路由注册完成
2025-08-02 21:27:13,092 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:27:13,093 - root - INFO - 配置加载完成
2025-08-02 21:27:13,093 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 21:27:13,093 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 21:27:13,094 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:27:13,094 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:27:13,095 - root - INFO - 应用路由注册完成
2025-08-02 21:27:13,095 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 21:27:13,101 - root - INFO - 加载配置文件: config/config.json
2025-08-02 21:27:13,101 - root - INFO - 配置加载完成
2025-08-02 21:27:13,103 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 21:27:13,104 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2/h5
2025-08-02 21:27:13,104 - root - INFO - 应用路由注册完成
2025-08-02 21:27:13,105 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 21:27:25,720 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:27:25 +0800] "GET /h5/index.html HTTP/1.1" 304 179 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:27:27,230 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 21:27:27,231 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:27:27 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 200 839 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:27:30,951 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:27:30,954 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:27:32,020 - root - INFO - AI模型响应状态: 200
2025-08-02 21:28:01,331 - root - INFO - 发送了 3980 个数据块给客户端
2025-08-02 21:28:01,336 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:27:30 +0800] "POST /v1/chat/completions HTTP/1.1" 200 461944 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:28:49,532 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:28:49,543 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:28:51,383 - root - INFO - AI模型响应状态: 200
2025-08-02 21:29:16,688 - root - INFO - 发送了 3422 个数据块给客户端
2025-08-02 21:29:16,715 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:28:49 +0800] "POST /v1/chat/completions HTTP/1.1" 200 397546 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:31:17,825 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:31:17,836 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:31:19,825 - root - INFO - AI模型响应状态: 200
2025-08-02 21:31:46,259 - root - INFO - 发送了 3566 个数据块给客户端
2025-08-02 21:31:46,283 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:31:17 +0800] "POST /v1/chat/completions HTTP/1.1" 200 414345 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:32:43,668 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:32:43,670 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:32:45,441 - root - INFO - AI模型响应状态: 200
2025-08-02 21:33:13,101 - root - INFO - 发送了 3734 个数据块给客户端
2025-08-02 21:33:13,106 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:32:43 +0800] "POST /v1/chat/completions HTTP/1.1" 200 433412 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:34:31,343 - root - INFO - 收到completions请求: 报告规范...
2025-08-02 21:34:31,346 - root - INFO - 转发请求到AI模型: https://hczqai.hczq.com:8443/api/chat/completions
2025-08-02 21:34:32,438 - root - INFO - AI模型响应状态: 200
2025-08-02 21:34:58,212 - root - INFO - 发送了 3516 个数据块给客户端
2025-08-02 21:34:58,213 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:21:34:31 +0800] "POST /v1/chat/completions HTTP/1.1" 200 408053 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 21:35:56,890 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 21:35:56,893 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 21:35:56,893 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 21:35:56,894 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 21:35:56,907 - root - INFO - 应用已关闭
2025-08-02 22:44:43,828 - root - INFO - 加载配置文件: config/config.json
2025-08-02 22:44:43,828 - root - INFO - 配置加载完成
2025-08-02 22:44:43,828 - root - INFO - 启动应用, 地址: 0.0.0.0:82, 工作进程数: 4
2025-08-02 22:44:43,829 - root - INFO - 在非 Windows 系统上运行多进程模式
2025-08-02 22:44:43,839 - root - INFO - 启动工作进程 #1 (PID: 79488)
2025-08-02 22:44:43,843 - root - INFO - 启动工作进程 #2 (PID: 79489)
2025-08-02 22:44:43,850 - root - INFO - 启动工作进程 #3 (PID: 79490)
2025-08-02 22:44:43,861 - root - INFO - 启动工作进程 #4 (PID: 79491)
2025-08-02 22:44:44,462 - root - INFO - 加载配置文件: config/config.json
2025-08-02 22:44:44,463 - root - INFO - 配置加载完成
2025-08-02 22:44:44,465 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 22:44:44,466 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2b/h5
2025-08-02 22:44:44,467 - root - INFO - 应用路由注册完成
2025-08-02 22:44:44,468 - root - INFO - MySQL连接已禁用，跳过连接池初始化
2025-08-02 22:44:44,469 - worker-0 - INFO - 工作进程 #0 开始监听端口 82
2025-08-02 22:44:44,471 - root - INFO - 加载配置文件: config/config.json
2025-08-02 22:44:44,472 - root - INFO - 配置加载完成
2025-08-02 22:44:44,474 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 22:44:44,475 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2b/h5
2025-08-02 22:44:44,475 - root - INFO - 应用路由注册完成
2025-08-02 22:44:44,476 - root - INFO - MySQL连接已禁用，跳过连接池初始化
2025-08-02 22:44:44,477 - worker-1 - INFO - 工作进程 #1 开始监听端口 82
2025-08-02 22:44:44,478 - root - INFO - 加载配置文件: config/config.json
2025-08-02 22:44:44,478 - root - INFO - 配置加载完成
2025-08-02 22:44:44,480 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 22:44:44,480 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2b/h5
2025-08-02 22:44:44,481 - root - INFO - 应用路由注册完成
2025-08-02 22:44:44,481 - root - INFO - MySQL连接已禁用，跳过连接池初始化
2025-08-02 22:44:44,482 - worker-2 - INFO - 工作进程 #2 开始监听端口 82
2025-08-02 22:44:44,486 - root - INFO - 加载配置文件: config/config.json
2025-08-02 22:44:44,486 - root - INFO - 配置加载完成
2025-08-02 22:44:44,487 - root - INFO - MySQL连接已禁用，跳过连接池处理器注册
2025-08-02 22:44:44,488 - root - INFO - 注册静态目录: /Users/<USER>/Desktop/massprod2b/h5
2025-08-02 22:44:44,488 - root - INFO - 应用路由注册完成
2025-08-02 22:44:44,489 - root - INFO - MySQL连接已禁用，跳过连接池初始化
2025-08-02 22:44:44,489 - worker-3 - INFO - 工作进程 #3 开始监听端口 82
2025-08-02 22:45:24,959 - root - INFO - 读取消息记录请求: groupId=0
2025-08-02 22:45:24,959 - root - INFO - MySQL连接已禁用，返回空的聊天消息
2025-08-02 22:45:24,959 - root - ERROR - 读取消息记录失败: name 'filtered_buttons' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/massprod2b/main.py", line 375, in read_config_handler
    ret_data = {'ui': config['ui'], 'buttons': filtered_buttons, 'chatMsgs': ''}
NameError: name 'filtered_buttons' is not defined
2025-08-02 22:45:24,961 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:22:45:24 +0800] "GET /v1/config?groupId=0 HTTP/1.1" 500 234 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 22:45:25,044 - aiohttp.access - INFO - 127.0.0.1 [02/Aug/2025:22:45:25 +0800] "GET /favicon.ico HTTP/1.1" 404 175 "http://localhost:82/h5/index.html" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-08-02 22:48:55,054 - root - INFO - 收到终止信号，关闭所有工作进程
2025-08-02 22:48:55,055 - root - INFO - MySQL连接池未初始化，跳过关闭操作
2025-08-02 22:48:55,055 - root - INFO - MySQL连接池未初始化，跳过关闭操作
2025-08-02 22:48:55,056 - root - INFO - MySQL连接池未初始化，跳过关闭操作
2025-08-02 22:48:55,055 - root - INFO - MySQL连接池未初始化，跳过关闭操作
2025-08-02 22:48:55,056 - worker-3 - INFO - 工作进程 #3 已关闭
2025-08-02 22:48:55,065 - worker-2 - INFO - 工作进程 #2 已关闭
2025-08-02 22:48:55,067 - worker-1 - INFO - 工作进程 #1 已关闭
2025-08-02 22:48:55,085 - root - INFO - 应用已关闭
