import json
import multiprocessing
import os
import asyncio
import logging
import socket
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

import aiohttp
import aiomysql
from aiohttp import web, ClientSession, ClientResponseError
import aiohttp_cors


# 配置日志系统
def setup_logger():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 文件处理器 - 每天轮转，保留7天
    file_handler = TimedRotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        when='midnight',
        interval=1,
        backupCount=7
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    return logger


# 初始化日志记录器
logger = setup_logger()

# 公安内网
'''
API_URL = 'http://41.190.25.156:80/ali-qw3-235b/v1/chat/completions'
API_KEY = 'ac161187152a48318f00'
model = 'Qwen/Qwen3-235B-A22B'
'''


# 加载提示词和知识库
def load_config():
    system_prompt = {}
    try:
        config_path = 'config/config.json'
        if os.name == 'nt':
            config_path = 'config/config_win.json'

        logger.info(f"加载配置文件: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            _config = json.load(f)

        with open('config/buttons.json', 'r', encoding='utf-8') as f:
            buttons = json.load(f)
            _config['buttons'] = buttons

        return _config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}", exc_info=True)
        raise


# 初始化应用时加载配置
try:
    config = load_config()
    logger.info("配置加载完成")
except Exception as e:
    logger.critical("配置加载失败，应用无法启动", exc_info=True)
    raise


def forward_to_ai_model(messages, model_params, chatMsgs=False):
    """使用aiohttp转发请求到AI模型API"""
    try:
        full_context = []
        with open('config/prompt/knowledgebase.txt', 'r', encoding='utf-8') as f:
            knowledge = f.read()
            full_context.append({"role": "system", "content": knowledge})

        if chatMsgs:
            chatInfos = '# 以下是【警情信息】:\n"""\n' + chatMsgs + '\n"""\n\n'
            full_context.append({"role": "user", "content": chatInfos})

        question = messages[-1].get('content').strip()
        isQuestion = True
        for button in config['buttons']:
            if question == button['command']:
                isQuestion = False
                with open(f'config/prompt/{button["promptfile"]}', 'r', encoding='utf-8') as f:
                    prompt = f.read()
                    full_context.append({"role": "system", "content": prompt})

        if isQuestion:
            full_context.append({"role": "user", "content": question})

        # 构建请求体
        request_body = {
            "messages": full_context,
            "model": config['ai']['model'],
            "stream": True,
            **model_params
        }

        headers = {
            "Content-Type": "application/json",
            "token": config['ai']['api_key'],
            "Authorization": f"Bearer {config['ai']['api_key']}",
        }

        logger.debug(f"AI请求体构建完成: {request_body}")
        return headers, request_body
    except Exception as e:
        logger.error(f"构建AI请求失败: {str(e)}", exc_info=True)
        raise


async def completions(request):
    """处理客户端聊天请求"""
    try:
        data = await request.json()
        logger.info(f"收到completions请求: {data.get('messages', [])[-1].get('content', '')[:50]}...")
    except json.JSONDecodeError:
        logger.warning("无效的JSON数据")
        return web.json_response(
            {"error": "无效的JSON数据"},
            status=400
        )

    # 从请求中提取参数
    messages = data.get("messages", [])
    chatMsgs = data.get("chatMsgs", False)
    model_params = {
        "temperature": config['ai'].get("temperature", 0.1),
        "top_p": config['ai'].get("top_p", 0.2),
        "max_tokens": config['ai'].get("max_tokens", 2048),
        "frequency_penalty": config['ai'].get("frequency_penalty", 0.0),
        "presence_penalty": config['ai'].get("presence_penalty", 0.0),
        "top_k": config['ai'].get("top_k", 5),
        "enable_thinking": config['ai'].get("enable_thinking", True),
        "stop": config['ai'].get("stop", ["Observation"])
    }

    # 创建流式响应
    response = web.StreamResponse(
        status=200,
        reason='OK',
        headers={
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        }
    )
    await response.prepare(request)

    try:
        # 转发请求到AI模型
        headers, request_body = forward_to_ai_model(messages, model_params, chatMsgs)
        logger.info(f"转发请求到AI模型: {config['ai']['api_url']}")

        timeout = aiohttp.ClientTimeout(total=600)
        async with ClientSession(timeout=timeout) as session:
            try:
                async with session.post(
                        config['ai']['api_url'],
                        json=request_body,
                        headers=headers,
                        timeout=600
                ) as ai_response:
                    if ai_response.status != 200:
                        error_detail = await ai_response.text()
                        logger.error(f"AI模型API错误: {ai_response.status} - {error_detail}")
                        raise Exception(f"AI模型API错误: {ai_response.status} - {error_detail}")

                    logger.info(f"AI模型响应状态: {ai_response.status}")
                    chunk_count = 0

                    # 将AI模型的流式响应转发给客户端
                    async for chunk in ai_response.content:
                        await response.write(chunk)
                        chunk_count += 1

                    logger.info(f"发送了 {chunk_count} 个数据块给客户端")
            except ClientResponseError as e:
                logger.error(f"AI模型请求失败: {e.status} - {e.message}", exc_info=True)
                raise Exception(f"AI模型请求失败: {e.status} - {e.message}")
            except asyncio.TimeoutError:
                logger.error("AI模型请求超时")
                raise Exception("AI模型请求超时")

    except Exception as e:
        logger.error(f"请求处理失败: {str(e)}", exc_info=True)
        error_event = json.dumps({
            "error": str(e),
            "finish_reason": "error"
        })
        await response.write(f"data: {error_event}\n\n".encode('utf-8'))
    finally:
        try:
            await response.write_eof()
        except Exception:
            pass

    return response


# websocket
async def websocket_handler(request):
    ws = web.WebSocketResponse()
    await ws.prepare(request)
    logger.info(f"WebSocket 连接建立: {request.remote}")

    try:
        async for msg in ws:
            if msg.type == web.WSMsgType.TEXT:
                try:
                    logger.debug(f"收到WebSocket消息: {msg.data[:100]}...")
                    data = json.loads(msg.data)

                    # 提取参数
                    messages = data.get("messages", [])
                    chatMsgs = data.get("chatMsgs", False)
                    model_params = data.get("model_params", {
                        "temperature": config['ai'].get("temperature", 0.1),
                        "top_p": config['ai'].get("top_p", 0.2),
                        "max_tokens": config['ai'].get("max_tokens", 2048),
                        "frequency_penalty": config['ai'].get("frequency_penalty", 0.0),
                        "presence_penalty": config['ai'].get("presence_penalty", 0.0),
                        "stop": config['ai'].get("stop", ["Observation"])
                    })

                    logger.info(f"处理WebSocket请求: {messages[-1].get('content', '')[:50]}...")

                    # 构建请求到 AI 模型
                    headers, request_body = forward_to_ai_model(messages, model_params, chatMsgs)

                    # 异步调用 AI 模型 API
                    timeout = aiohttp.ClientTimeout(total=600)
                    async with aiohttp.ClientSession(timeout=timeout) as session:
                        async with session.post(
                                config['ai']['api_url'],
                                json=request_body,
                                headers=headers,
                                timeout=600
                        ) as ai_response:

                            if ai_response.status != 200:
                                error_detail = await ai_response.text()
                                error_msg = {"error": f"AI模型API错误: {ai_response.status} - {error_detail}"}
                                logger.error(f"AI模型错误: {error_detail}")
                                await ws.send_str(json.dumps(error_msg))
                                continue

                            logger.info(f"AI模型响应状态: {ai_response.status}")
                            chunk_count = 0

                            # 流式转发响应内容
                            async for chunk in ai_response.content:
                                if chunk:
                                    await ws.send_str(chunk.decode('utf-8'))
                                    chunk_count += 1

                            logger.info(f"通过WebSocket发送了 {chunk_count} 个数据块")

                except json.JSONDecodeError:
                    logger.warning("无效的JSON数据")
                    await ws.send_str(json.dumps({"error": "无效的JSON数据"}))
                except Exception as e:
                    logger.error(f"WebSocket处理异常: {str(e)}", exc_info=True)
                    await ws.send_str(json.dumps({"error": str(e)}))

            elif msg.type == web.WSMsgType.CLOSE:
                logger.info("WebSocket 客户端关闭连接")
            elif msg.type == web.WSMsgType.ERROR:
                logger.error(f"WebSocket 发生错误: {ws.exception()}")
    except Exception as e:
        logger.error(f"WebSocket处理失败: {str(e)}", exc_info=True)
    finally:
        logger.info(f"WebSocket 连接关闭: {request.remote}")

    return ws


async def prompt_manage_handler(request):
    # 遍历配置文件
    prompt_dir = 'config/prompt'
    request_json = await request.json()
    pwd = request_json.get('pwd', '')
    act = request_json.get('act', '')
    filename = request_json.get('filename', '')
    content = request_json.get('content', '')
    if pwd != 'ltkj123' or act == '':
        return web.json_response({"code": 1, "msg": "密码错误"}, status=200)

    if act == 'save' and filename and content:
        if filename == 'buttons.json':
            file_path = os.path.join('config', filename)
            try:
                json.loads(content)
            except json.JSONDecodeError:
                return web.json_response({"code": 1, "msg": "JSON格式错误"}, status=200)
        else:
            file_path = os.path.join(prompt_dir, filename)
            if not filename.endswith('.txt'):
                return web.json_response({"code": 1, "msg": "文件名错误"}, status=200)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            return web.json_response({"code": 0, "msg": "保存成功"}, status=200)

    if act == 'read':
        # 遍历文件夹
        prompt_files = []

        for root, dirs, files in os.walk(prompt_dir):
            # 遍历所有文件，筛选出.txt文件
            for file in files:
                if not file.endswith('.txt'):
                    continue
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    prompt_files.append({'file': file, 'file_path': file_path, 'content': content})

        with open('config/buttons.json', 'r', encoding='utf-8') as f:
            buttons = json.load(f)

        return web.json_response({"buttons": buttons, "prompt_files": prompt_files}, status=200)
    pass


async def read_config_handler(request):
    """处理客户端对消息记录的读取请求"""
    groupId = request.query.get('groupId', 'none')
    logger.info(f"读取消息记录请求: groupId={groupId}")

    try:
        if groupId == 'none' or groupId == '0':
            # 不带groupId参数　
            buttons = [button for button in config['buttons'] if button.get("group") != 1]
            ret_data = {'ui': config['ui'], 'buttons': buttons, 'chatMsgs': ''}
            return web.json_response({"data": ret_data}, status=200)

        pool = request.app['mysql_pool']
        sql = 'SELECT * FROM message where msgState=0 and msgType="TextMedia" and groupId=%s order by sendTime'

        chatMsgs = []
        async with pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cur:
                await cur.execute(sql, [groupId])
                messages = await cur.fetchall()
                logger.info(f"从数据库获取到 {len(messages)} 条消息记录")
                textLength = 0
                for item in messages:
                    msg = {}
                    timestamp = int(item['sendTime'] / 1000)
                    msg['datetime'] = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    if item['sendType'] == 'staff':
                        msg['user'] = '民警'
                    else:
                        msg['user'] = '系统'
                    try:
                        msgData = json.loads(item['msgData'])
                        msg['text'] = msgData['text']
                    except json.JSONDecodeError:
                        msg['text'] = item['msgData']
                    chatMsgs.append(msg)
                    textLength = textLength + len(msg['text'])

            if textLength < 50:
                chatMsgs = ''
        buttons = [button for button in config['buttons'] if button.get("group") != 2]
        ret_data = {'ui': config['ui'], 'buttons': buttons, 'chatMsgs': chatMsgs}
        return web.json_response({"data": ret_data}, status=200)
    except Exception as e:
        logger.error(f"读取消息记录失败: {str(e)}", exc_info=True)
        return web.json_response({"error": "内部服务器错误"}, status=500)


async def init_mysql_pool(app):
    try:
        logger.info("初始化MySQL连接池...")
        app['mysql_pool'] = await aiomysql.create_pool(
            host=config['mysql']['host'],
            user=config['mysql']['user'],
            password=config['mysql']['password'],
            db=config['mysql']['database'],
            charset=config['mysql']['charset'],
            autocommit=True
        )
        logger.info("MySQL连接池初始化完成")
    except Exception as e:
        logger.critical(f"MySQL连接池初始化失败: {str(e)}", exc_info=True)
        raise


# 清理连接池
async def close_mysql_pool(app):
    try:
        logger.info("关闭MySQL连接池...")
        app['mysql_pool'].close()
        await app['mysql_pool'].wait_closed()
        logger.info("MySQL连接池已关闭")
    except Exception as e:
        logger.error(f"关闭MySQL连接池时出错: {str(e)}", exc_info=True)


async def init_app():
    app = web.Application()
    app.on_startup.append(init_mysql_pool)
    app.on_cleanup.append(close_mysql_pool)

    # 配置CORS
    cors = aiohttp_cors.setup(app, defaults={
        "*": aiohttp_cors.ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*",
            allow_methods="*"
        )
    })

    # 注册静态文件目录 h5
    static_dir = os.path.join(os.getcwd(), 'h5')
    if os.path.exists(static_dir):
        app.router.add_static('/h5/', static_dir, name='static_h5')
        logger.info(f"注册静态目录: {static_dir}")
    else:
        logger.warning(f"静态目录不存在: {static_dir}")

    # 注册路由
    resource = cors.add(app.router.add_resource("/v1/chat/completions"))
    cors.add(resource.add_route("POST", completions))

    cors.add(app.router.add_resource("/v1/config")).add_route("GET", read_config_handler)
    cors.add(app.router.add_resource("/v1/prompt_manage")).add_route("POST", prompt_manage_handler)
    # 注册 WebSocket 路由
    cors.add(app.router.add_resource("/ws")).add_route("GET", websocket_handler)

    logger.info("应用路由注册完成")
    return app


# 新添加的辅助函数
def start_server(sock, worker_id):
    """启动单个工作进程的服务器"""
    # 为每个进程创建独立的日志记录器
    logger = logging.getLogger(f"worker-{worker_id}")
    logger.setLevel(logging.INFO)

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        f'%(asctime)s - Worker {worker_id} - %(levelname)s - %(message)s'
    ))
    logger.addHandler(console_handler)

    # 创建应用实例
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    app = loop.run_until_complete(init_app())

    # 启动服务器
    runner = web.AppRunner(app)
    loop.run_until_complete(runner.setup())

    # 使用共享套接字
    site = web.SockSite(runner, sock, shutdown_timeout=60)
    loop.run_until_complete(site.start())

    logger.info(f"工作进程 #{worker_id} 开始监听端口 {sock.getsockname()[1]}")

    try:
        loop.run_forever()
    except KeyboardInterrupt:
        pass
    finally:
        loop.run_until_complete(runner.cleanup())
        loop.close()
        logger.info(f"工作进程 #{worker_id} 已关闭")


if __name__ == "__main__":

    host = os.environ.get("HOST", "0.0.0.0")
    port = int(os.environ.get("PORT", 80))
    worker_count = multiprocessing.cpu_count()  # 获取 CPU 核心数

    logger.info(f"启动应用, 地址: {host}:{port}, 工作进程数: {worker_count}")

    if os.name == 'nt':  # Windows 系统
        logger.info("在 Windows 系统上运行单进程模式")
        # web.run_app(init_app(), host=host, port=port)
        worker_count = 1
    else:  # 非 Windows 系统（Linux/macOS）
        logger.info("在非 Windows 系统上运行多进程模式")

    # 创建共享套接字
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    sock.bind((host, port))
    sock.listen(100)

    # 创建工作进程
    workers = []
    for i in range(worker_count):
        worker = multiprocessing.Process(
            target=start_server,
            args=(sock, i)
        )
        worker.daemon = True
        worker.start()
        workers.append(worker)
        logger.info(f"启动工作进程 #{i + 1} (PID: {worker.pid})")

    try:
        # 主进程等待所有工作进程结束
        for worker in workers:
            worker.join()
    except KeyboardInterrupt:
        logger.info("收到终止信号，关闭所有工作进程")
        for worker in workers:
            worker.terminate()
            worker.join()
    finally:
        sock.close()

    logger.info("应用已关闭")
