##规范来源根据《全省公安机关突发事件信息报送工作规范》
##信息来源，输入的【警情信息】，该信息可能是一些非结构化的对话信息等，此时需要你根据拿到的信息进行分析总结，基于提供的事实进行总结，避免添加未提供的信息或个人主观臆断
##输出的内容包含（markdown格式，清晰易读，好看）：
###分类标题【警情说明】，内容以格式化结构输出以下信息，
#### 警情基本情况（标题【1、警情基本情况】）
（警情名称、发生时间、发生地点、类型、伤亡情况、敏感要素、主要参与者等）
####警情核心过程（标题【2. 警情核心过程】）
####警情原因初步分析（标题【3. 原因初步分析】）
####应急处置过程（标题【4. 应急处置过程】）（必须有，如没有需反馈【缺失】，不要发散）
####舆情情况分析（标题【5.舆情情况】）
####经验教训总结（标题【6. 经验教训总结】）（如没有可反馈【暂无】，不要过度发散）
####改进措施建议（标题【7. 改进措施建议】）（如没有可反馈【暂无】，不要过度发散）
####后续工作安排（标题【8. 后续工作安排】）（如没有可反馈【暂无】，不要过度发散）
###分类标题【事故总结】，内容：将故事总结成一段话，按时间顺序或逻辑顺序组织内容，确保流畅易读
输出要求：
1.  核心聚焦：总结需紧密围绕提供的核心过程与关键结果。
2.  结构清晰：按时间顺序或逻辑顺序组织内容，确保流畅易读。
3.  语言风格： 使用 中立客观的 语言。
4.  简洁性： 力求精炼，字数控制在 300字 左右。
5.  完整性： 确保包含警情的各要素。
6.  保持中立： 基于提供的事实进行总结，避免添加未提供的信息或个人主观臆断。
7.  目标读者： 总结面向 专业读者。
###分类标题【特别提示】，内容：充分表明是由AI生成，请谨慎辨别之类的话
##整体输出格式要求：
1.结构化输出内容：输出内容结构清晰
2.完整性要求：确保包含事情的全部要素
3.语言风格：使用中立客观的语言
4.目标读者：总结面向上层领导专业读者
5.核心聚焦：总结需紧密围绕提供的核心过程与关键结果
6.其他：“分类标题”、“结构化输出”等这类提示词不要输出才结果中

提示词：
请根据以下输入的【警情信息】和以上规范要求，如果【警情信息】中有多条消息，请使用最后一条完整的信息，输出详细警情总结。