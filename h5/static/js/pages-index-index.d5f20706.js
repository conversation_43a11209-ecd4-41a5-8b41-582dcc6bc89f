(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index"],{1082:function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,"[contenteditable=true].empty[data-v-36ffc0a4]::before{content:attr(data-placeholder);color:#999;position:absolute;cursor:text}[contenteditable=true][data-v-36ffc0a4]{position:relative;min-height:1em;outline:none;padding:8px;border:1px solid #ddd;border-radius:4px;background-color:#fff;transition:border-color .3s}[contenteditable=true][data-v-36ffc0a4]:focus{border-color:#409eff;box-shadow:0 0 5px rgba(64,158,255,.3)}[contenteditable=true]:focus.empty[data-v-36ffc0a4]::before{display:none}",""]),e.exports=t},"17bf":function(e,t,n){n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("aa9c"),e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,u,a,o=[],s=!0,l=!1;try{if(u=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=u.call(n)).done)&&(o.push(r.value),o.length!==t);s=!0);}catch(c){l=!0,i=c}finally{try{if(!s&&null!=n["return"]&&(a=n["return"](),Object(a)!==a))return}finally{if(l)throw i}}return o}},e.exports.__esModule=!0,e.exports["default"]=e.exports},1991:function(e,t,n){"use strict";n.r(t);var r=n("e8eb"),i=n.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(u);t["default"]=i.a},"1dbd":function(e,t,n){"use strict";var r=n("ac5f"),i=n("1fc1"),u=n("41c7"),a=n("ae5c"),o=function(e,t,n,s,l,c,h,d){var f,p,D=l,v=0,g=!!h&&a(h,d);while(v<s)v in n&&(f=g?g(n[v],v,t):n[v],c>0&&r(f)?(p=i(f),D=o(e,t,f,p,D,c-1)-1):(u(D+1),e[D]=f),D++),v++;return D};e.exports=o},"1ea2":function(e,t,n){"use strict";var r=n("af9e"),i=n("1c06"),u=n("ada5"),a=n("5d6e"),o=Object.isExtensible,s=r((function(){o(1)}));e.exports=s||a?function(e){return!!i(e)&&((!a||"ArrayBuffer"!==u(e))&&(!o||o(e)))}:o},"20f3":function(e,t,n){"use strict";var r=n("8bdb"),i=n("5145");r({target:"Array",proto:!0,forced:i!==[].lastIndexOf},{lastIndexOf:i})},2634:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t.default=function(){return e};var e={},n=Object.prototype,i=n.hasOwnProperty,u=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(z){c=function(e,t,n){return e[t]=n}}function h(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),o=new A(r||[]);return u(a,"_invoke",{value:w(e,n,o)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(z){return{type:"throw",arg:z}}}e.wrap=h;var f={};function p(){}function D(){}function v(){}var g={};c(g,o,(function(){return this}));var b=Object.getPrototypeOf,x=b&&b(b(B([])));x&&x!==n&&i.call(x,o)&&(g=x);var m=v.prototype=p.prototype=Object.create(g);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){var n;u(this,"_invoke",{value:function(u,a){function o(){return new t((function(n,o){(function n(u,a,o,s){var l=d(e[u],e,a);if("throw"!==l.type){var c=l.arg,h=c.value;return h&&"object"==(0,r.default)(h)&&i.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(h).then((function(e){c.value=e,o(c)}),(function(e){return n("throw",e,o,s)}))}s(l.arg)})(u,a,n,o)}))}return n=n?n.then(o,o):o()}})}function w(e,t,n){var r="suspendedStart";return function(i,u){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw u;return _()}for(n.method=i,n.arg=u;;){var a=n.delegate;if(a){var o=y(a,n);if(o){if(o===f)continue;return o}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=d(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function y(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator["return"]&&(t.method="return",t.arg=void 0,y(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;var i=d(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,f;var u=i.arg;return u?u.done?(t[e.resultName]=u.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):u:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function F(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function B(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:_}}function _(){return{value:void 0,done:!0}}return D.prototype=v,u(m,"constructor",{value:v,configurable:!0}),u(v,"constructor",{value:D,configurable:!0}),D.displayName=c(v,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===D||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,c(e,l,"GeneratorFunction")),e.prototype=Object.create(m),e},e.awrap=function(e){return{__await:e}},k(C.prototype),c(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,r,i,u){void 0===u&&(u=Promise);var a=new C(h(t,n,r,i),u);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(m),c(m,l,"Generator"),c(m,o,(function(){return this})),c(m,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=B,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(F),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var u=this.tryEntries[r],a=u.completion;if("root"===u.tryLoc)return n("end");if(u.tryLoc<=this.prev){var o=i.call(u,"catchLoc"),s=i.call(u,"finallyLoc");if(o&&s){if(this.prev<u.catchLoc)return n(u.catchLoc,!0);if(this.prev<u.finallyLoc)return n(u.finallyLoc)}else if(o){if(this.prev<u.catchLoc)return n(u.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return n(u.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var u=r;break}}u&&("break"===e||"continue"===e)&&u.tryLoc<=t&&t<=u.finallyLoc&&(u=null);var a=u?u.completion:{};return a.type=e,a.arg=t,u?(this.method="next",this.next=u.finallyLoc,f):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),F(n),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;F(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:B(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),f}},e},n("6a54"),n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("3872e"),n("4e9b"),n("114e"),n("c240"),n("926e"),n("7a76"),n("c9b5"),n("aa9c"),n("2797"),n("8a8d"),n("dc69"),n("f7a5");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("fcf3"))},2774:function(e,t,n){n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb"),e.exports=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"2fdc":function(e,t,n){"use strict";function r(e,t,n,r,i,u,a){try{var o=e[u](a),s=o.value}catch(l){return void n(l)}o.done?t(s):Promise.resolve(s).then(r,i)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=this,n=arguments;return new Promise((function(i,u){var a=e.apply(t,n);function o(e){r(a,i,u,o,s,"next",e)}function s(e){r(a,i,u,o,s,"throw",e)}o(void 0)}))}},n("bf0f")},3471:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=(0,r.default)(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var i=0,u=function(){};return{s:u,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(s)throw a}}}},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("5d6b"))},4085:function(e,t,n){"use strict";var r=n("8bdb"),i=n("85c1");r({global:!0,forced:i.globalThis!==i},{globalThis:i})},4550:function(e,t,n){n("6a54");var r=n("8bcf");e.exports=function(e,t,n){return t=r(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"49b4":function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("2634")),u=r(n("3471")),a=r(n("2fdc"));n("d4b5"),n("5c47"),n("af8f"),n("bf0f"),n("18f7"),n("de6c"),n("2425"),n("aa9c"),n("7a76"),n("c9b5"),n("c223"),n("0c26"),n("a1c1"),n("4626"),n("5ac7"),n("5ef2"),n("f7a5");var o=r(n("877d")),s=r(n("fba7")),l=r(n("cdd1")),c={components:{ContentEditable:o.default,compareVue:l.default},data:function(){return{base_url:"",inputShow:!1,htmlContent:"",isChated:!1,loading:!1,currindex:-1,notification:{show:!1,text:"",icon:"fa-check-circle"},userMessage:"",config:{apiEndpoint:"",wsEndpoint:"",title:"警情编报 AI助手系统",sub_title:"基于《全省公安机关突发事件信息报送工作规范》.",referenceDoc:"《全省公安机关突发事件信息报送工作规范》.",chatMsgs:null,groupId:!1},aiobj:{sended:!1,isEditing:!1,isStreaming:!1,reasoningCollapsed:!1,reasoningCollapsed_one:!0,reasoningContent:"",streamingContent:""},buttons:[{command:"hidden",label:"问答",icon:"fa-question-circle",promptfile:""}]}},onLoad:function(){var e=this;console.log(JSON.stringify(this.buttons)),this.base_url=this.getBaseUrl(),this.config.apiEndpoint=this.base_url+"/v1/chat/completions",this.config.wsEndpoint=this.base_url+"/ws";var t=window.location.search.toLowerCase(),n=new URLSearchParams(t),r=n.get("groupid");r?this.config.groupId=r:(uni.showToast({title:"问答模式,请先输入内容",duration:3e3,icon:"none"}),r="0"),uni.showLoading({mask:!0,title:"正在加载数据 ..."}),fetch(this.base_url+"/v1/config?groupId="+r).then((function(e){return e.json()})).then((function(t){Object.assign(e.config,t.data.ui),uni.hideLoading();var n=t.data.buttons;if(n){n.push({command:"hidden",label:"问答",icon:"fa-question-circle",promptfile:""});for(var i=0;i<n.length;i++)Object.assign(n[i],e.aiobj);e.buttons=n}t.data.chatMsgs?e.config.chatMsgs=JSON.stringify(t.data.chatMsgs):(e.config.chatMsgs="","0"!==r&&""===e.config.chatMsgs&&(e.config.groupId=!1,uni.showToast({title:"没有读取到足够的警情内容，请手工补充",duration:3e3,icon:"none"}))),e.showNotification("警情编报系统已就绪")}))},methods:{getBaseUrl:function(){var e=window.location.port;return"8080"===e?"http://localhost:80":""},chkMsg:function(){if(this.config.groupId)return!0;if(this.htmlContent.length<50)return this.showNotification("内容不能少于50字"),this.inputShow=!0,!1;if(this.config.chatMsgs&&this.config.chatMsgs!==this.htmlContent){for(var e=0;e<this.buttons.length;e++)Object.assign(this.buttons[e],this.aiobj);this.showNotification("已经编辑内容，AI重新生成")}return this.config.chatMsgs=this.htmlContent,this.inputShow=!1,!0},command:function(e,t){this.chkMsg()&&(this.currindex=t,this.buttons[t].sended||(this.buttons[t].sended=!0,this.streamAI(e.command,this.buttons[t])))},showNotification:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"fa-check-circle";this.notification.text=e,this.notification.icon=n,this.notification.show=!0,setTimeout((function(){t.notification.show=!1}),3e3)},toggleEditMode:function(e){this.buttons[e].isEditing=!this.buttons[e].isEditing,this.buttons[e].isEditing&&this.showNotification("已进入编辑模式，可直接修改内容")},copyToClipboard:function(e){return navigator.clipboard?navigator.clipboard.writeText(e):new Promise((function(t,n){var r=document.createElement("textarea");r.value=e,r.style.position="fixed",document.body.appendChild(r),r.select();try{var i=document.execCommand("copy");document.body.removeChild(r),i?t():n(new Error("复制失败"))}catch(u){document.body.removeChild(r),n(u)}}))},copyContent:function(e){var t=this,n=document.getElementById("markdown-content-main_".concat(e));if(n){var r=n.innerText;this.copyToClipboard(r).then((function(){return t.showNotification("报告内容已复制到剪贴板")})).catch((function(e){return console.error("复制失败:",e)}))}},parseMarkdown:function(e){return e?s.default.parse(e):""},toggleReasoning:function(e){this.buttons[e].reasoningCollapsed=!this.buttons[e].reasoningCollapsed},scrollToBottom:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$nextTick((function(){var t=document.querySelector(".auto-scroll-container");if(t){var n=t.scrollTop+t.clientHeight;(e||n>=t.scrollHeight-100)&&(t.scrollTop=t.scrollHeight)}}))},streamAI:function(e,t){var n=this;return(0,a.default)((0,i.default)().mark((function r(){var a,o,s,l,c,h,d,f,p,D,v,g,b,x,m,k,C,w,y;return(0,i.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n.isChated=!0,t.isStreaming=!0,t.streamingContent="",t.reasoningContent="",t.reasoningCollapsed=!1,n.loading=!0,n.scrollToBottom(!0),r.prev=7,a={model:"",messages:[{role:"user",content:e}],stream:!0,chatMsgs:n.config.chatMsgs},r.next=11,fetch(n.config.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});case 11:if(o=r.sent,o.ok){r.next=14;break}throw new Error("请求失败: ".concat(o.status," ").concat(o.statusText));case 14:s=o.body.getReader(),l=new TextDecoder("utf-8"),c="",h="",d=-1,f=!1;case 20:return r.next=23,s.read();case 23:if(p=r.sent,D=p.value,v=p.done,!v){r.next=28;break}return r.abrupt("break",57);case 28:c+=l.decode(D,{stream:!0}),g=c.split("\n"),c=g.pop()||"",b=(0,u.default)(g),r.prev=32,b.s();case 34:if((x=b.n()).done){r.next=47;break}if(m=x.value,""!==m.trim()){r.next=38;break}return r.abrupt("continue",45);case 38:if(k=m.replace(/^data:/,"").trim(),!k.includes("[DONE]")){r.next=43;break}return t.isStreaming=!1,n.loading=!1,r.abrupt("return");case 43:try{C=JSON.parse(k),C.choices&&C.choices.length>0&&(w=C.choices[0],w.delta.reasoning_content&&(f=!0,t.reasoningContent+=w.delta.reasoning_content,n.scrollToBottom()),w.delta.content&&(f?(t.reasoningCollapsed_one&&(n.scrollToBottom(!0),t.reasoningCollapsed=!0,t.reasoningCollapsed_one=!1),t.streamingContent+=w.delta.content,n.scrollToBottom()):(h+=w.delta.content,h.length<9&&h.length>6&&h.includes("<think>")&&(d=h.indexOf("<think>")),d>-1&&(y=h.indexOf("</think>"),-1===y?t.reasoningContent=h.substring(d+7):(t.reasoningContent=h.slice(d+7,y),d=-1,h=h.substring(y+8))),h.length>9&&-1===d&&(t.reasoningCollapsed_one&&(n.scrollToBottom(!0),t.reasoningCollapsed=!0,t.reasoningCollapsed_one=!1),t.streamingContent=h,n.scrollToBottom()))))}catch(i){console.error("JSON解析错误:",i,k)}n.$forceUpdate();case 45:r.next=34;break;case 47:r.next=52;break;case 49:r.prev=49,r.t0=r["catch"](32),b.e(r.t0);case 52:return r.prev=52,b.f(),r.finish(52);case 55:r.next=20;break;case 57:r.next=65;break;case 59:r.prev=59,r.t1=r["catch"](7),t.sended=!1,console.error("AI调用失败:",r.t1),t.streamingContent="### AI处理失败\n```\n".concat(r.t1.message,"\n```\n请检查API设置或网络连接"),n.loading=!1;case 65:return r.prev=65,t.isStreaming=!1,n.loading=!1,r.finish(65);case 69:case"end":return r.stop()}}),r,null,[[7,59,65,69],[32,49,52,55]])})))()},inputFocus:function(){this.buttons[this.buttons.length-1].sended&&(this.currindex=this.buttons.length-1)},sendMessage:function(){var e=this;return(0,a.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.chkMsg()){t.next=2;break}return t.abrupt("return");case 2:if(e.userMessage.trim()){t.next=5;break}return e.showNotification("请输入警情描述","fa-exclamation-circle"),t.abrupt("return");case 5:return e.currindex=e.buttons.length-1,e.buttons[e.currindex].sended=!0,t.next=9,e.streamAI(e.userMessage,e.buttons[e.buttons.length-1]);case 9:setTimeout((function(){e.userMessage=""}),500);case 10:case"end":return t.stop()}}),t)})))()}}};t.default=c},"4cf1":function(e,t,n){n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=n("79b7");e.exports=function(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var i=0,u=function(){};return{s:u,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(s)throw a}}}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4db6":function(e,t,n){n("6a54");var r=n("8bcf");function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}e.exports=function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4f05":function(e,t,n){"use strict";var r=n("ee98").start,i=n("8b27");e.exports=i("trimStart")?function(){return r(this)}:"".trimStart},5014:function(e,t,n){n("dc8a"),n("01a2"),n("8f71"),n("bf0f"),n("9a2c"),n("aa9c"),n("2797"),n("a644"),n("a03a"),n("6a54");var r=n("4550");function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},5075:function(e,t,n){"use strict";var r=n("ae5c"),i=n("71e9"),u=n("e7e3"),a=n("52df"),o=n("81a7"),s=n("1fc1"),l=n("1297"),c=n("d67c"),h=n("5112"),d=n("7e91"),f=TypeError,p=function(e,t){this.stopped=e,this.result=t},D=p.prototype;e.exports=function(e,t,n){var v,g,b,x,m,k,C,w=n&&n.that,y=!(!n||!n.AS_ENTRIES),E=!(!n||!n.IS_RECORD),F=!(!n||!n.IS_ITERATOR),A=!(!n||!n.INTERRUPTED),B=r(t,w),_=function(e){return v&&d(v,"normal",e),new p(!0,e)},z=function(e){return y?(u(e),A?B(e[0],e[1],_):B(e[0],e[1])):A?B(e,_):B(e)};if(E)v=e.iterator;else if(F)v=e;else{if(g=h(e),!g)throw new f(a(e)+" is not iterable");if(o(g)){for(b=0,x=s(e);x>b;b++)if(m=z(e[b]),m&&l(D,m))return m;return new p(!1)}v=c(e,g)}k=E?e.next:v.next;while(!(C=i(k,v)).done){try{m=z(C.value)}catch(S){d(v,"throw",S)}if("object"==typeof m&&m&&l(D,m))return m}return new p(!1)}},"53f7":function(e,t,n){"use strict";var r=n("7658"),i=n("57e7");r("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},"56c9":function(e,t,n){n("9e15"),n("884b"),n("01a2"),n("e39c"),n("bf0f"),n("7a76"),n("c9b5"),n("64aa");var r=n("bdbb")["default"];e.exports=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"57e7":function(e,t,n){"use strict";var r=n("e37c"),i=n("e4ca"),u=n("a74c"),a=n("ae5c"),o=n("b720"),s=n("1eb8"),l=n("5075"),c=n("0cc2"),h=n("97ed"),d=n("437f"),f=n("ab4a"),p=n("d0b1").fastKey,D=n("235c"),v=D.set,g=D.getterFor;e.exports={getConstructor:function(e,t,n,c){var h=e((function(e,i){o(e,d),v(e,{type:t,index:r(null),first:void 0,last:void 0,size:0}),f||(e.size=0),s(i)||l(i,e[c],{that:e,AS_ENTRIES:n})})),d=h.prototype,D=g(t),b=function(e,t,n){var r,i,u=D(e),a=x(e,t);return a?a.value=n:(u.last=a={index:i=p(t,!0),key:t,value:n,previous:r=u.last,next:void 0,removed:!1},u.first||(u.first=a),r&&(r.next=a),f?u.size++:e.size++,"F"!==i&&(u.index[i]=a)),e},x=function(e,t){var n,r=D(e),i=p(t);if("F"!==i)return r.index[i];for(n=r.first;n;n=n.next)if(n.key===t)return n};return u(d,{clear:function(){var e=D(this),t=e.first;while(t)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),t=t.next;e.first=e.last=void 0,e.index=r(null),f?e.size=0:this.size=0},delete:function(e){var t=D(this),n=x(this,e);if(n){var r=n.next,i=n.previous;delete t.index[n.index],n.removed=!0,i&&(i.next=r),r&&(r.previous=i),t.first===n&&(t.first=r),t.last===n&&(t.last=i),f?t.size--:this.size--}return!!n},forEach:function(e){var t,n=D(this),r=a(e,arguments.length>1?arguments[1]:void 0);while(t=t?t.next:n.first){r(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!x(this,e)}}),u(d,n?{get:function(e){var t=x(this,e);return t&&t.value},set:function(e,t){return b(this,0===e?0:e,t)}}:{add:function(e){return b(this,e=0===e?0:e,e)}}),f&&i(d,"size",{configurable:!0,get:function(){return D(this).size}}),h},setStrong:function(e,t,n){var r=t+" Iterator",i=g(t),u=g(r);c(e,t,(function(e,t){v(this,{type:r,target:e,state:i(e),kind:t,last:void 0})}),(function(){var e=u(this),t=e.kind,n=e.last;while(n&&n.removed)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?h("keys"===t?n.key:"values"===t?n.value:[n.key,n.value],!1):(e.target=void 0,h(void 0,!0))}),n?"entries":"values",!n,!0),d(t)}}},"5d6e":function(e,t,n){"use strict";var r=n("af9e");e.exports=r((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}}))},6178:function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"61cc":function(e,t,n){"use strict";var r=n("ac1f"),i=n.n(r);i.a},"6e12":function(e,t,n){"use strict";n("73c2");var r=n("8bdb"),i=n("ab3f");r({target:"String",proto:!0,name:"trimEnd",forced:"".trimEnd!==i},{trimEnd:i})},"71cb":function(e,t,n){"use strict";var r=n("8bdb"),i=n("1dbd"),u=n("1099"),a=n("1fc1"),o=n("497b"),s=n("3242");r({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=u(this),n=a(t),r=s(t,0);return r.length=i(r,t,t,n,0,void 0===e?1:o(e)),r}})},7277:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"popup-container"},[n("div",{staticClass:"popup-header"},[n("h1",[n("i",{staticClass:"fas fa-mobile-alt"}),e._v(e._s(e.config.title))]),n("p",{staticStyle:{"margin-left":"5px"}},[e._v(e._s(e.config.sub_title))])]),n("div",{staticClass:"auto-scroll-container"},[n("div",{staticClass:"ai-response"},[n("ContentEditable",{directives:[{name:"show",rawName:"v-show",value:e.inputShow,expression:"inputShow"}],ref:"myedit",staticClass:"scroll-container",model:{value:e.htmlContent,callback:function(t){e.htmlContent=t},expression:"htmlContent"}}),e.isChated||e.inputShow?e._e():n("div",{staticClass:"scroll-container"},[n("div",{staticClass:"markdown-content"},[n("div",{staticClass:"response-content"},[n("h3",[e._v(e._s(e.config.title))]),n("p",[e._v("本系统依据"+e._s(e.config.referenceDoc)+"开发，提供以下功能：")]),e._m(0),e._m(1)])])]),e._l(e.buttons,(function(t,r){return e.isChated&&!e.inputShow?n("div",{directives:[{name:"show",rawName:"v-show",value:r===e.currindex,expression:"index === currindex"}],key:r,staticClass:"response-content"},[t.isStreaming&&!t.streamingContent?n("div",{staticClass:"thinking-indicator"},[n("i",{staticClass:"fas fa-cog fa-spin"}),n("span",[e._v("正在思考中，请稍候...")])]):n("div",{staticClass:"reasoning-toggle",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleReasoning(r)}}},[t.reasoningCollapsed?n("i",{staticClass:"fas fa-chevron-right"}):n("i",{staticClass:"fas fa-chevron-down"}),n("span",[e._v("思考过程 "+e._s(t.reasoningCollapsed?"(点击展开)":""))])]),n("div",{staticClass:"reasoning-content",class:{collapsed:t.reasoningCollapsed}},[e._v(e._s(t.reasoningContent))]),n("div",{directives:[{name:"show",rawName:"v-show",value:t.streamingContent,expression:"item.streamingContent"}],staticClass:"scroll-container",attrs:{contenteditable:t.isEditing}},[n("div",{staticClass:"markdown-content",attrs:{id:"markdown-content-main_"+r},domProps:{innerHTML:e._s(e.parseMarkdown(t.streamingContent))}})]),t.streamingContent&&!t.isStreaming?n("div",{staticClass:"action-buttons"},[n("div",{staticClass:"action-btn",class:{active:t.isEditing},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleEditMode(r)}}},[n("i",{staticClass:"fas",class:t.isEditing?"fa-save":"fa-edit"}),e._v(e._s(t.isEditing?"退出编辑":"编辑"))]),n("div",{staticClass:"action-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.copyContent(r)}}},[n("i",{staticClass:"fas fa-copy"}),e._v("复制")]),1==t.compare?n("compareVue",{attrs:{"original-text":e.htmlContent,"ai-text":t.streamingContent}}):e._e()],1):e._e()]):e._e()}))],2)]),n("div",{staticClass:"message-container"},[n("div",{staticClass:"function-buttons"},[e.config.groupId?e._e():n("a",{staticClass:"func-btn",staticStyle:{background:"linear-gradient(135deg, #118246, #00f852)"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.inputShow=!e.inputShow}}},[n("i",{staticClass:"fas fa-info"}),e._v("输入内容")]),e._l(e.buttons,(function(t,r){return n("a",{staticClass:"func-btn",class:{"func-btn2":e.currindex==r},style:{display:"hidden"===t.command?"none":""},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.command(t,r)}}},[n("i",{class:"fas "+t.icon}),e._v(e._s(t.label))])}))],2),n("div",{staticStyle:{display:"flex","flex-direction":"row",width:"100%","justify-content":"space-between"}},[n("div",{staticClass:"message-box"},[n("v-uni-input",{attrs:{type:"text",placeholder:"请点击上方↑按钮或输入问题 ......"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.inputFocus.apply(void 0,arguments)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;arguments[0]=t=e.$handleEvent(t),e.sendMessage.apply(void 0,arguments)}},model:{value:e.userMessage,callback:function(t){e.userMessage=t},expression:"userMessage"}})],1),n("div",{staticClass:"func-btn",staticStyle:{width:"15%","text-align":"center",display:"flex","justify-content":"center"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.sendMessage.apply(void 0,arguments)}}},[n("i",{staticClass:"fas fa-paper-plane"}),e._v("发送(回车)")])])]),n("div",{staticClass:"notification",class:{show:e.notification.show}},[n("i",{class:["fas",e.notification.icon]}),e._v(e._s(e.notification.text))])])},i=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ul",[n("li",[n("strong",[e._v("信息报送判断")]),e._v("：根据警情性质判断是否符合报送标准")]),n("li",[n("strong",[e._v("报送依据引用")]),e._v("：自动关联规范中的具体条款")]),n("li",[n("strong",[e._v("编写初报")]),e._v("：一键生成符合规范的警情报告")]),n("li",[n("strong",[e._v("报送范围查询")]),e._v("：快速查询各类突发警情报送标准")]),n("li",[n("strong",[e._v("警情总结")]),e._v("：生成完整的警情分析报告")])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"edit-hint"},[t("i",{staticClass:"fas fa-lightbulb"}),this._v("请使用下方功能按钮进行分析，或直接输入警情信息进行报送判断")])}]},7340:function(e,t,n){"use strict";var r=n("8bdb"),i=n("4f05");r({target:"String",proto:!0,name:"trimStart",forced:"".trimLeft!==i},{trimLeft:i})},"73c2":function(e,t,n){"use strict";var r=n("8bdb"),i=n("ab3f");r({target:"String",proto:!0,name:"trimEnd",forced:"".trimRight!==i},{trimRight:i})},"73f1":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,'.container[data-v-4a2334c6]{padding:20px;min-height:100vh;background-color:#f5f5f5}.trigger-btn[data-v-4a2334c6]{background-color:#83acd9;color:#fff;border:none;border-radius:6px;font-size:16px;cursor:pointer}\n\n/* 遮罩层 */.modal-mask[data-v-4a2334c6]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:999}\n\n/* 弹出窗口 */.modal-container[data-v-4a2334c6]{position:fixed;background-color:#fff;border-radius:8px;box-shadow:0 4px 20px rgba(0,0,0,.15);display:flex;flex-direction:column;z-index:1000;min-width:300px;min-height:300px}\n\n/* 标题栏 */.modal-header[data-v-4a2334c6]{background-color:#f8f8f8;padding:6px 16px;border-bottom:1px solid #e5e5e5;border-radius:8px 8px 0 0;cursor:move;display:flex;justify-content:space-between;align-items:center;-webkit-user-select:none;user-select:none}.modal-title[data-v-4a2334c6]{font-size:16px;font-weight:600;color:#333}.header-controls[data-v-4a2334c6]{display:flex;gap:8px}.control-btn[data-v-4a2334c6]{width:24px;height:24px;display:flex;align-items:center;justify-content:center;border-radius:4px;cursor:pointer;transition:background-color .2s}.control-btn[data-v-4a2334c6]:hover{background-color:#e0e0e0}.control-btn.close[data-v-4a2334c6]:hover{background-color:#f44;color:#fff}.control-icon[data-v-4a2334c6]{font-size:14px;line-height:1}\n\n/* 内容区域 */.modal-content[data-v-4a2334c6]{flex:1;padding:6px;overflow:hidden;display:flex;flex-direction:column}.compare-container[data-v-4a2334c6]{display:flex;height:100%;gap:0}.text-panel[data-v-4a2334c6]{display:flex;flex-direction:column;border:1px solid #e5e5e5;border-radius:6px;overflow:hidden;height:100%}.left-panel[data-v-4a2334c6]{margin-right:-1px\n\t/* 避免边框重叠 */}.right-panel[data-v-4a2334c6]{margin-left:-1px\n\t/* 避免边框重叠 */}.panel-header[data-v-4a2334c6]{background-color:#f8f8f8;padding:8px 12px;border-bottom:1px solid #e5e5e5;display:flex;align-items:center}.panel-title[data-v-4a2334c6]{font-size:14px;font-weight:500;color:#666}.text-content[data-v-4a2334c6]{flex:1;padding:0;overflow:hidden;background-color:#fff}\n\n/* 可编辑文本区域 */.editable-text[data-v-4a2334c6]{width:100%;height:100%;padding:16px;border:none;outline:none;font-size:14px;line-height:1.8;background-color:initial;box-sizing:border-box;overflow-y:auto;white-space:pre-wrap;word-wrap:break-word}\n\n/* 原文样式 */.editable-text.original[data-v-4a2334c6]{color:#333;font-family:Courier New,monospace;background-color:#fafafa;cursor:default}\n\n/* AI文本样式 */.editable-text.ai-text[data-v-4a2334c6]{color:#2c3e50;font-family:Arial,sans-serif;background-color:#f8f9fa;border-left:3px solid #007aff}\n\n/* 编辑状态样式 */.editable-text[contenteditable="true"][data-v-4a2334c6]:focus{background-color:#fff;box-shadow:inset 0 0 0 2px rgba(0,122,255,.3)}.editable-text.ai-text[contenteditable="true"][data-v-4a2334c6]:focus{background-color:#f0f4f8}\n\n/* 分隔线 */.divider[data-v-4a2334c6]{width:8px;background-color:#e5e5e5;position:relative;display:flex;align-items:center;justify-content:center;cursor:col-resize;-webkit-user-select:none;user-select:none;transition:background-color .2s}.divider[data-v-4a2334c6]:hover{background-color:#007aff}.divider-handle[data-v-4a2334c6]{width:4px;height:40px;background-color:#999;border-radius:2px;transition:background-color .2s}.divider:hover .divider-handle[data-v-4a2334c6]{background-color:#fff}\n\n/* 调整大小的手柄 */.resize-handle[data-v-4a2334c6]{position:absolute;background-color:initial;z-index:10}\n\n/* 四个角 */.resize-handle.top-left[data-v-4a2334c6]{top:0;left:0;width:10px;height:10px;cursor:nw-resize}.resize-handle.top-right[data-v-4a2334c6]{top:0;right:0;width:10px;height:10px;cursor:ne-resize}.resize-handle.bottom-left[data-v-4a2334c6]{bottom:0;left:0;width:10px;height:10px;cursor:sw-resize}.resize-handle.bottom-right[data-v-4a2334c6]{bottom:0;right:0;width:10px;height:10px;cursor:se-resize}\n\n/* 四条边 */.resize-handle.top[data-v-4a2334c6]{top:0;left:10px;right:10px;height:5px;cursor:n-resize}.resize-handle.right[data-v-4a2334c6]{top:10px;right:0;bottom:10px;width:5px;cursor:e-resize}.resize-handle.bottom[data-v-4a2334c6]{bottom:0;left:10px;right:10px;height:5px;cursor:s-resize}.resize-handle.left[data-v-4a2334c6]{top:10px;left:0;bottom:10px;width:5px;cursor:w-resize}\n\n/* 手柄悬停效果 */.resize-handle[data-v-4a2334c6]:hover{background-color:rgba(0,122,255,.3)}',""]),e.exports=t},7658:function(e,t,n){"use strict";var r=n("8bdb"),i=n("85c1"),u=n("bb80"),a=n("8466"),o=n("81a9"),s=n("d0b1"),l=n("5075"),c=n("b720"),h=n("474f"),d=n("1eb8"),f=n("1c06"),p=n("af9e"),D=n("29ba"),v=n("181d"),g=n("dcda");e.exports=function(e,t,n){var b=-1!==e.indexOf("Map"),x=-1!==e.indexOf("Weak"),m=b?"set":"add",k=i[e],C=k&&k.prototype,w=k,y={},E=function(e){var t=u(C[e]);o(C,e,"add"===e?function(e){return t(this,0===e?0:e),this}:"delete"===e?function(e){return!(x&&!f(e))&&t(this,0===e?0:e)}:"get"===e?function(e){return x&&!f(e)?void 0:t(this,0===e?0:e)}:"has"===e?function(e){return!(x&&!f(e))&&t(this,0===e?0:e)}:function(e,n){return t(this,0===e?0:e,n),this})},F=a(e,!h(k)||!(x||C.forEach&&!p((function(){(new k).entries().next()}))));if(F)w=n.getConstructor(t,e,b,m),s.enable();else if(a(e,!0)){var A=new w,B=A[m](x?{}:-0,1)!==A,_=p((function(){A.has(1)})),z=D((function(e){new k(e)})),S=!x&&p((function(){var e=new k,t=5;while(t--)e[m](t,t);return!e.has(-0)}));z||(w=t((function(e,t){c(e,C);var n=g(new k,e,w);return d(t)||l(t,n[m],{that:n,AS_ENTRIES:b}),n})),w.prototype=C,C.constructor=w),(_||S)&&(E("delete"),E("has"),b&&E("get")),(S||B)&&E(m),x&&C.clear&&delete C.clear}return y[e]=w,r({global:!0,constructor:!0,forced:w!==k},y),v(w,e),x||n.setStrong(w,e,b),w}},"79a2":function(e,t,n){"use strict";n.r(t);var r=n("cdc3"),i=n.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(u);t["default"]=i.a},"79b7":function(e,t,n){n("f7a5"),n("bf0f"),n("08eb"),n("18f7"),n("5c47"),n("0506");var r=n("e476");e.exports=function(e,t){if(e){if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},8663:function(e,t,n){"use strict";var r=n("d2f0"),i=n.n(r);i.a},"877d":function(e,t,n){"use strict";n.r(t);var r=n("d68f"),i=n("1991");for(var u in i)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(u);n("8663");var a=n("828b"),o=Object(a["a"])(i["default"],r["b"],r["c"],!1,null,"36ffc0a4",null,!1,r["a"],void 0);t["default"]=o.exports},"883d":function(e,t,n){n("7a76"),n("c9b5"),e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"8bcf":function(e,t,n){var r=n("bdbb")["default"],i=n("56c9");e.exports=function(e){var t=i(e,"string");return"symbol"===r(t)?t:String(t)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"8e4d":function(e,t,n){n("7a76"),n("c9b5"),e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},9376:function(e,t,n){n("7a76"),n("c9b5"),e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports["default"]=e.exports},a45a:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",[n("v-uni-button",{staticClass:"trigger-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showModal=!0}}},[e._v("打开原文比对")]),e.showModal?n("v-uni-view",{staticClass:"container modal-mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleMaskClick.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"modal-container",style:e.modalStyle,on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[n("v-uni-view",{staticClass:"modal-header",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startDrag.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"modal-title"},[e._v("文本对比窗口")]),n("v-uni-view",{staticClass:"header-controls"},[n("v-uni-view",{staticClass:"control-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleMaximize.apply(void 0,arguments)}}},[n("v-uni-text",{staticClass:"control-icon"},[e._v(e._s(e.isMaximized?"❐":"□"))])],1),n("v-uni-view",{staticClass:"control-btn close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showModal=!1}}},[n("v-uni-text",{staticClass:"control-icon"},[e._v("×")])],1)],1)],1),n("v-uni-view",{staticClass:"modal-content"},[n("v-uni-view",{staticClass:"compare-container"},[n("v-uni-view",{staticClass:"text-panel left-panel",style:{width:e.leftPanelWidth+"%"}},[n("v-uni-view",{staticClass:"panel-header"},[n("v-uni-text",{staticClass:"panel-title"},[e._v("原文")])],1),n("v-uni-view",{staticClass:"text-content"},[n("div",{ref:"originalTextRef",staticClass:"editable-text original",attrs:{contenteditable:"true"}})])],1),n("v-uni-view",{staticClass:"divider",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResize.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"divider-handle"})],1),n("v-uni-view",{staticClass:"text-panel right-panel",style:{width:100-e.leftPanelWidth+"%"}},[n("v-uni-view",{staticClass:"panel-header"},[n("v-uni-text",{staticClass:"panel-title"},[e._v("AI文本")])],1),n("v-uni-view",{staticClass:"text-content"},[n("div",{ref:"aiTextRef",staticClass:"markdown-content",staticStyle:{padding:"16px","overflow-y":"auto",height:"100%"},attrs:{contenteditable:"true"}})])],1)],1)],1),e.isMaximized?e._e():[n("v-uni-view",{staticClass:"resize-handle top-left",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("top-left")}}}),n("v-uni-view",{staticClass:"resize-handle top-right",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("top-right")}}}),n("v-uni-view",{staticClass:"resize-handle bottom-left",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("bottom-left")}}}),n("v-uni-view",{staticClass:"resize-handle bottom-right",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("bottom-right")}}}),n("v-uni-view",{staticClass:"resize-handle top",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("top")}}}),n("v-uni-view",{staticClass:"resize-handle right",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("right")}}}),n("v-uni-view",{staticClass:"resize-handle bottom",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("bottom")}}}),n("v-uni-view",{staticClass:"resize-handle left",on:{mousedown:function(t){arguments[0]=t=e.$handleEvent(t),e.startResizeWindow("left")}}})]],2)],1):e._e()],1)},i=[]},a578:function(e,t,n){"use strict";var r=n("8bdb"),i=n("f298");r({target:"String",proto:!0},{repeat:i})},ab3f:function(e,t,n){"use strict";var r=n("ee98").end,i=n("8b27");e.exports=i("trimEnd")?function(){return r(this)}:"".trimEnd},ac1f:function(e,t,n){var r=n("73f1");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var i=n("967d").default;i("6ce0a684",r,!0,{sourceMap:!1,shadowMode:!1})},c238:function(e,t,n){"use strict";var r=n("af9e");e.exports=!r((function(){return Object.isExtensible(Object.preventExtensions({}))}))},c275:function(e,t,n){"use strict";n.r(t);var r=n("7277"),i=n("c8e5");for(var u in i)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(u);var a=n("828b"),o=Object(a["a"])(i["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=o.exports},c8e5:function(e,t,n){"use strict";n.r(t);var r=n("49b4"),i=n.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(u);t["default"]=i.a},cdc3:function(e,t,n){"use strict";n("6a54");var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(n("fba7")),u={name:"TextCompareModal",props:{originalText:{type:String,default:""},aiText:{type:String,default:""}},data:function(){return{showModal:!1,isMaximized:!1,isDragging:!1,isResizing:!1,isResizingWindow:!1,resizeDirection:"",dragStartX:0,dragStartY:0,modalX:0,modalY:0,modalWidth:.8*uni.getSystemInfoSync().windowWidth,modalHeight:.8*uni.getSystemInfoSync().windowHeight,leftPanelWidth:40,startX:0,startWidth:0,startModalX:0,startModalY:0,startModalWidth:0,startModalHeight:0,boundOnResize:null,boundStopResize:null}},computed:{modalStyle:function(){return this.isMaximized?{left:"0",top:"0",width:"100%",height:"100%",transform:"none"}:{left:this.modalX+"px",top:this.modalY+"px",width:this.modalWidth+"px",height:this.modalHeight+"px",transform:"none"}}},watch:{originalText:{handler:function(e){this.updateOriginalTextContent()},immediate:!0},aiText:{handler:function(e){this.updateAiTextContent()},immediate:!0},showModal:function(e){var t=this;e&&this.$nextTick((function(){t.centerModal(),t.updateTextContents()}))}},methods:{parseMarkdown:function(e){return e?i.default.parse(e):""},handleMaskClick:function(){this.showModal=!1},toggleMaximize:function(){this.isMaximized=!this.isMaximized},startDrag:function(e){this.isMaximized||(this.isDragging=!0,this.dragStartX=e.clientX-this.modalX,this.dragStartY=e.clientY-this.modalY,document.addEventListener("mousemove",this.onDrag),document.addEventListener("mouseup",this.stopDrag))},onDrag:function(e){this.isDragging&&(this.modalX=e.clientX-this.dragStartX,this.modalY=e.clientY-this.dragStartY)},stopDrag:function(){this.isDragging=!1,document.removeEventListener("mousemove",this.onDrag),document.removeEventListener("mouseup",this.stopDrag)},startResize:function(e){e.stopPropagation(),e.preventDefault(),this.isResizing=!0,this.startX=e.clientX,this.startWidth=this.leftPanelWidth,this.boundOnResize=this.handlePanelResize.bind(this),this.boundStopResize=this.stopPanelResize.bind(this),document.addEventListener("mousemove",this.boundOnResize),document.addEventListener("mouseup",this.boundStopResize)},handlePanelResize:function(e){if(this.isResizing&&(e.preventDefault(),this.$el)){var t=this.$el.querySelector(".compare-container");if(t){var n=e.clientX-this.startX,r=t.clientWidth;if(!(r<=0)){var i=this.startWidth+n/r*100;this.leftPanelWidth=Math.max(20,Math.min(80,i))}}}},stopPanelResize:function(){this.isResizing&&(this.isResizing=!1,this.boundOnResize&&(document.removeEventListener("mousemove",this.boundOnResize),this.boundOnResize=null),this.boundStopResize&&(document.removeEventListener("mouseup",this.boundStopResize),this.boundStopResize=null))},startResizeWindow:function(e){this.isMaximized||(this.isResizingWindow=!0,this.resizeDirection=e,this.startModalX=this.modalX,this.startModalY=this.modalY,this.startModalWidth=this.modalWidth,this.startModalHeight=this.modalHeight,this.dragStartX=event.clientX,this.dragStartY=event.clientY,document.addEventListener("mousemove",this.onResizeWindow),document.addEventListener("mouseup",this.stopResizeWindow))},onResizeWindow:function(e){if(this.isResizingWindow){var t=e.clientX-this.dragStartX,n=e.clientY-this.dragStartY;switch(this.resizeDirection){case"top":this.modalY=this.startModalY+n,this.modalHeight=Math.max(300,this.startModalHeight-n);break;case"right":this.modalWidth=Math.max(300,this.startModalWidth+t);break;case"bottom":this.modalHeight=Math.max(300,this.startModalHeight+n);break;case"left":this.modalX=this.startModalX+t,this.modalWidth=Math.max(300,this.startModalWidth-t);break;case"top-left":this.modalX=this.startModalX+t,this.modalY=this.startModalY+n,this.modalWidth=Math.max(300,this.startModalWidth-t),this.modalHeight=Math.max(300,this.startModalHeight-n);break;case"top-right":this.modalY=this.startModalY+n,this.modalWidth=Math.max(300,this.startModalWidth+t),this.modalHeight=Math.max(300,this.startModalHeight-n);break;case"bottom-left":this.modalX=this.startModalX+t,this.modalWidth=Math.max(300,this.startModalWidth-t),this.modalHeight=Math.max(300,this.startModalHeight+n);break;case"bottom-right":this.modalWidth=Math.max(300,this.startModalWidth+t),this.modalHeight=Math.max(300,this.startModalHeight+n);break}}},stopResizeWindow:function(){this.isResizingWindow=!1,document.removeEventListener("mousemove",this.onResizeWindow),document.removeEventListener("mouseup",this.stopResizeWindow)},updateOriginalTextContent:function(){this.$refs.originalTextRef&&(this.$refs.originalTextRef.innerHTML=this.originalText)},updateAiTextContent:function(){this.$refs.aiTextRef&&(this.$refs.aiTextRef.innerHTML=this.parseMarkdown(this.aiText))},updateTextContents:function(){this.updateOriginalTextContent(),this.updateAiTextContent()},centerModal:function(){var e=window.innerWidth,t=window.innerHeight;this.modalX=(e-this.modalWidth)/2,this.modalY=(t-this.modalHeight)/2}},beforeDestroy:function(){this.boundOnResize&&document.removeEventListener("mousemove",this.boundOnResize),this.boundStopResize&&document.removeEventListener("mouseup",this.boundStopResize),document.removeEventListener("mousemove",this.onResizeWindow),document.removeEventListener("mouseup",this.stopResizeWindow)}};t.default=u},cdd1:function(e,t,n){"use strict";n.r(t);var r=n("a45a"),i=n("79a2");for(var u in i)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(u);n("61cc");var a=n("828b"),o=Object(a["a"])(i["default"],r["b"],r["c"],!1,null,"4a2334c6",null,!1,r["a"],void 0);t["default"]=o.exports},d0b1:function(e,t,n){"use strict";var r=n("8bdb"),i=n("bb80"),u=n("11bf"),a=n("1c06"),o=n("338c"),s=n("d6b1").f,l=n("80bb"),c=n("8449"),h=n("1ea2"),d=n("d7b4"),f=n("c238"),p=!1,D=d("meta"),v=0,g=function(e){s(e,D,{value:{objectID:"O"+v++,weakData:{}}})},b=e.exports={enable:function(){b.enable=function(){},p=!0;var e=l.f,t=i([].splice),n={};n[D]=1,e(n).length&&(l.f=function(n){for(var r=e(n),i=0,u=r.length;i<u;i++)if(r[i]===D){t(r,i,1);break}return r},r({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:c.f}))},fastKey:function(e,t){if(!a(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,D)){if(!h(e))return"F";if(!t)return"E";g(e)}return e[D].objectID},getWeakData:function(e,t){if(!o(e,D)){if(!h(e))return!0;if(!t)return!1;g(e)}return e[D].weakData},onFreeze:function(e){return f&&p&&h(e)&&!o(e,D)&&g(e),e}};u[D]=!0},d189:function(e,t,n){var r=n("e2db"),i=n("2774"),u=n("79b7"),a=n("9376");e.exports=function(e){return r(e)||i(e)||u(e)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},d2f0:function(e,t,n){var r=n("1082");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var i=n("967d").default;i("0e3fb25a",r,!0,{sourceMap:!1,shadowMode:!1})},d68f:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"editableDiv",class:{empty:e.isEmpty},attrs:{contenteditable:"true","data-placeholder":e.placeholder},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)}}})},i=[]},dda4:function(e,t,n){var r=n("6178"),i=n("17bf"),u=n("79b7"),a=n("8e4d");e.exports=function(e,t){return r(e)||i(e,t)||u(e,t)||a()},e.exports.__esModule=!0,e.exports["default"]=e.exports},e2db:function(e,t,n){var r=n("e476");e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},e476:function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports["default"]=e.exports},e6d5:function(e,t,n){"use strict";n("7340");var r=n("8bdb"),i=n("4f05");r({target:"String",proto:!0,name:"trimStart",forced:"".trimStart!==i},{trimStart:i})},e8eb:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("0c26");var r={name:"ContentEditable",props:{value:{type:String,default:""},text:{type:String,default:""},placeholder:{type:String,default:"请在此处输入警情内容..."}},data:function(){return{isEmpty:!0}},watch:{value:function(e){var t=this;this.$refs.editableDiv&&this.$refs.editableDiv.innerHTML!==e&&(this.$refs.editableDiv.innerHTML=e,this.$nextTick((function(){t.checkEmpty()})))}},onLoad:function(){this.$refs.editableDiv.innerHTML=this.value,this.checkEmpty()},methods:{checkEmpty:function(){if(this.$refs.editableDiv){var e=""!==this.$refs.editableDiv.innerText.trim();this.isEmpty=!e}},getText:function(){return this.$refs.editableDiv.innerText.trim()},onInput:function(){var e=this.$refs.editableDiv.innerHTML;this.$emit("input",e),this.checkEmpty()},onFocus:function(){this.isEmpty=!1},onBlur:function(){var e=this.$refs.editableDiv.innerHTML;this.$emit("input",e),this.$emit("blur"),this.checkEmpty()}}};t.default=r},f18a:function(e,t,n){"use strict";var r=n("1cb5");r("flat")},f298:function(e,t,n){"use strict";var r=n("497b"),i=n("9e70"),u=n("862c"),a=RangeError;e.exports=function(e){var t=i(u(this)),n="",o=r(e);if(o<0||o===1/0)throw new a("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(t+=t))1&o&&(n+=t);return n}},f3f7:function(e,t,n){"use strict";n("53f7")},fba7:function(e,t,n){var r,i,u,a=n("5014").default,o=n("d189").default,s=n("4cf1").default,l=n("883d").default,c=n("4db6").default,h=n("4550").default,d=n("dda4").default,f=n("bdbb").default;n("4085"),n("23f4"),n("7d2f"),n("5c47"),n("9c4e"),n("ab80"),n("0506"),n("a1c1"),n("dfcf"),n("0c26"),n("dd2b"),n("aa9c"),n("f7a5"),n("5ef2"),n("2c10"),n("fd3c"),n("c223"),n("a578"),n("e6d5"),n("af8f"),n("6e12"),n("8f71"),n("bf0f"),n("2797"),n("7a76"),n("c9b5"),n("dc8a"),n("4626"),n("5ac7"),n("20f3"),n("3efd"),n("71cb"),n("f18a"),n("18f7"),n("de6c"),n("f3f7"),function(n,a){"object"===f(t)&&"undefined"!==typeof e?a(t):(i=[t],r=a,u="function"===typeof r?r.apply(t,i):r,void 0===u||(e.exports=u))}(0,(function(e){"use strict";function t(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}function n(t){e.defaults=t}e.defaults=t();var r=/[&<>"']/,i=new RegExp(r.source,"g"),u=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,f=new RegExp(u.source,"g"),p={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},D=function(e){return p[e]};function v(e,t){if(t){if(r.test(e))return e.replace(i,D)}else if(u.test(e))return e.replace(f,D);return e}var g=/(^|[^\[])\^/g;function b(e,t){var n="string"===typeof e?e:e.source;t=t||"";var r={replace:function(e,t){var i="string"===typeof t?t:t.source;return i=i.replace(g,"$1"),n=n.replace(e,i),r},getRegex:function(){return new RegExp(n,t)}};return r}function x(e){try{e=encodeURI(e).replace(/%25/g,"%")}catch(t){return null}return e}var m={exec:function(){return null}};function k(e,t){var n=e.replace(/\|/g,(function(e,t,n){var r=!1,i=t;while(--i>=0&&"\\"===n[i])r=!r;return r?"|":" |"})),r=n.split(/ \|/),i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),t)if(r.length>t)r.splice(t);else while(r.length<t)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function C(e,t,n){var r=e.length;if(0===r)return"";var i=0;while(i<r){var u=e.charAt(r-i-1);if(u!==t||n){if(u===t||!n)break;i++}else i++}return e.slice(0,r-i)}function w(e,t,n,r){var i=t.href,u=t.title?v(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;var o={type:"link",raw:n,href:i,title:u,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,o}return{type:"image",raw:n,href:i,title:u,text:v(a)}}var y=function(){function t(n){l(this,t),h(this,"options",void 0),h(this,"rules",void 0),h(this,"lexer",void 0),this.options=n||e.defaults}return c(t,[{key:"space",value:function(e){var t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}},{key:"code",value:function(e){var t=this.rules.block.code.exec(e);if(t){var n=t[0].replace(/^(?: {1,4}| {0,3}\t)/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:C(n,"\n")}}}},{key:"fences",value:function(e){var t=this.rules.block.fences.exec(e);if(t){var n=t[0],r=function(e,t){var n=e.match(/^(\s+)(?:```)/);if(null===n)return t;var r=n[1];return t.split("\n").map((function(e){var t=e.match(/^\s+/);if(null===t)return e;var n=d(t,1),i=n[0];return i.length>=r.length?e.slice(r.length):e})).join("\n")}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}},{key:"heading",value:function(e){var t=this.rules.block.heading.exec(e);if(t){var n=t[2].trim();if(/#$/.test(n)){var r=C(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}},{key:"hr",value:function(e){var t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:C(t[0],"\n")}}},{key:"blockquote",value:function(e){var t=this.rules.block.blockquote.exec(e);if(t){var n=C(t[0],"\n").split("\n"),r="",i="",u=[];while(n.length>0){var a=!1,o=[],s=void 0;for(s=0;s<n.length;s++)if(/^ {0,3}>/.test(n[s]))o.push(n[s]),a=!0;else{if(a)break;o.push(n[s])}n=n.slice(s);var l=o.join("\n"),c=l.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,"\n    $1").replace(/^ {0,3}>[ \t]?/gm,"");r=r?"".concat(r,"\n").concat(l):l,i=i?"".concat(i,"\n").concat(c):c;var h=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(c,u,!0),this.lexer.state.top=h,0===n.length)break;var d=u[u.length-1];if("code"===(null===d||void 0===d?void 0:d.type))break;if("blockquote"===(null===d||void 0===d?void 0:d.type)){var f=d,p=f.raw+"\n"+n.join("\n"),D=this.blockquote(p);u[u.length-1]=D,r=r.substring(0,r.length-f.raw.length)+D.raw,i=i.substring(0,i.length-f.text.length)+D.text;break}if("list"!==(null===d||void 0===d?void 0:d.type));else{var v=d,g=v.raw+"\n"+n.join("\n"),b=this.list(g);u[u.length-1]=b,r=r.substring(0,r.length-d.raw.length)+b.raw,i=i.substring(0,i.length-v.raw.length)+b.raw,n=g.substring(u[u.length-1].raw.length).split("\n")}}return{type:"blockquote",raw:r,tokens:u,text:i}}}},{key:"list",value:function(e){var t=this.rules.block.list.exec(e);if(t){var n=t[1].trim(),r=n.length>1,i={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?"\\d{1,9}\\".concat(n.slice(-1)):"\\".concat(n),this.options.pedantic&&(n=r?n:"[*+-]");var u=new RegExp("^( {0,3}".concat(n,")((?:[\t ][^\\n]*)?(?:\\n|$))")),a=!1;while(e){var o=!1,s="",l="";if(!(t=u.exec(e)))break;if(this.rules.block.hr.test(e))break;s=t[0],e=e.substring(s.length);var c=t[2].split("\n",1)[0].replace(/^\t+/,(function(e){return" ".repeat(3*e.length)})),h=e.split("\n",1)[0],d=!c.trim(),f=0;if(this.options.pedantic?(f=2,l=c.trimStart()):d?f=t[1].length+1:(f=t[2].search(/[^ ]/),f=f>4?1:f,l=c.slice(f),f+=t[1].length),d&&/^[ \t]*$/.test(h)&&(s+=h+"\n",e=e.substring(h.length+1),o=!0),!o){var p=new RegExp("^ {0,".concat(Math.min(3,f-1),"}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))")),D=new RegExp("^ {0,".concat(Math.min(3,f-1),"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)")),v=new RegExp("^ {0,".concat(Math.min(3,f-1),"}(?:```|~~~)")),g=new RegExp("^ {0,".concat(Math.min(3,f-1),"}#")),b=new RegExp("^ {0,".concat(Math.min(3,f-1),"}<(?:[a-z].*>|!--)"),"i");while(e){var x=e.split("\n",1)[0],m=void 0;if(h=x,this.options.pedantic?(h=h.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  "),m=h):m=h.replace(/\t/g,"    "),v.test(h))break;if(g.test(h))break;if(b.test(h))break;if(p.test(h))break;if(D.test(h))break;if(m.search(/[^ ]/)>=f||!h.trim())l+="\n"+m.slice(f);else{if(d)break;if(c.replace(/\t/g,"    ").search(/[^ ]/)>=4)break;if(v.test(c))break;if(g.test(c))break;if(D.test(c))break;l+="\n"+h}d||h.trim()||(d=!0),s+=x+"\n",e=e.substring(x.length+1),c=m.slice(f)}}i.loose||(a?i.loose=!0:/\n[ \t]*\n[ \t]*$/.test(s)&&(a=!0));var k=null,C=void 0;this.options.gfm&&(k=/^\[[ xX]\] /.exec(l),k&&(C="[ ] "!==k[0],l=l.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:s,task:!!k,checked:C,loose:!1,text:l,tokens:[]}),i.raw+=s}i.items[i.items.length-1].raw=i.items[i.items.length-1].raw.trimEnd(),i.items[i.items.length-1].text=i.items[i.items.length-1].text.trimEnd(),i.raw=i.raw.trimEnd();for(var w=0;w<i.items.length;w++)if(this.lexer.state.top=!1,i.items[w].tokens=this.lexer.blockTokens(i.items[w].text,[]),!i.loose){var y=i.items[w].tokens.filter((function(e){return"space"===e.type})),E=y.length>0&&y.some((function(e){return/\n.*\n/.test(e.raw)}));i.loose=E}if(i.loose)for(var F=0;F<i.items.length;F++)i.items[F].loose=!0;return i}}},{key:"html",value:function(e){var t=this.rules.block.html.exec(e);if(t){var n={type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]};return n}}},{key:"def",value:function(e){var t=this.rules.block.def.exec(e);if(t){var n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:i}}}},{key:"table",value:function(e){var t=this,n=this.rules.block.table.exec(e);if(n&&/[:|]/.test(n[2])){var r=k(n[1]),i=n[2].replace(/^\||\| *$/g,"").split("|"),u=n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split("\n"):[],a={type:"table",raw:n[0],header:[],align:[],rows:[]};if(r.length===i.length){var o,l=s(i);try{for(l.s();!(o=l.n()).done;){var c=o.value;/^ *-+: *$/.test(c)?a.align.push("right"):/^ *:-+: *$/.test(c)?a.align.push("center"):/^ *:-+ *$/.test(c)?a.align.push("left"):a.align.push(null)}}catch(D){l.e(D)}finally{l.f()}for(var h=0;h<r.length;h++)a.header.push({text:r[h],tokens:this.lexer.inline(r[h]),header:!0,align:a.align[h]});var d,f=s(u);try{for(f.s();!(d=f.n()).done;){var p=d.value;a.rows.push(k(p,a.header.length).map((function(e,n){return{text:e,tokens:t.lexer.inline(e),header:!1,align:a.align[n]}})))}}catch(D){f.e(D)}finally{f.f()}return a}}}},{key:"lheading",value:function(e){var t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}},{key:"paragraph",value:function(e){var t=this.rules.block.paragraph.exec(e);if(t){var n="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}},{key:"text",value:function(e){var t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}},{key:"escape",value:function(e){var t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:v(t[1])}}},{key:"tag",value:function(e){var t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}},{key:"link",value:function(e){var t=this.rules.inline.link.exec(e);if(t){var n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;var r=C(n.slice(0,-1),"\\");if((n.length-r.length)%2===0)return}else{var i=function(e,t){if(-1===e.indexOf(t[1]))return-1;for(var n=0,r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}(t[2],"()");if(i>-1){var u=0===t[0].indexOf("!")?5:4,a=u+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,a).trim(),t[3]=""}}var o=t[2],s="";if(this.options.pedantic){var l=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(o);l&&(o=l[1],s=l[3])}else s=t[3]?t[3].slice(1,-1):"";return o=o.trim(),/^</.test(o)&&(o=this.options.pedantic&&!/>$/.test(n)?o.slice(1):o.slice(1,-1)),w(t,{href:o?o.replace(this.rules.inline.anyPunctuation,"$1"):o,title:s?s.replace(this.rules.inline.anyPunctuation,"$1"):s},t[0],this.lexer)}}},{key:"reflink",value:function(e,t){var n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){var r=(n[2]||n[1]).replace(/\s+/g," "),i=t[r.toLowerCase()];if(!i){var u=n[0].charAt(0);return{type:"text",raw:u,text:u}}return w(n,i,n[0],this.lexer)}}},{key:"emStrong",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=this.rules.inline.emStrongLDelim.exec(e);if(r&&(!r[3]||!n.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDCD0-\uDCEB\uDCF0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])/))){var i=r[1]||r[2]||"";if(!i||!n||this.rules.inline.punctuation.exec(n)){var u,a,s=o(r[0]).length-1,l=s,c=0,h="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;h.lastIndex=0,t=t.slice(-1*e.length+s);while(null!=(r=h.exec(t)))if(u=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],u)if(a=o(u).length,r[3]||r[4])l+=a;else if(!((r[5]||r[6])&&s%3)||(s+a)%3){if(l-=a,!(l>0)){a=Math.min(a,a+l+c);var d=o(r[0])[0].length,f=e.slice(0,s+r.index+d+a);if(Math.min(s,a)%2){var p=f.slice(1,-1);return{type:"em",raw:f,text:p,tokens:this.lexer.inlineTokens(p)}}var D=f.slice(2,-2);return{type:"strong",raw:f,text:D,tokens:this.lexer.inlineTokens(D)}}}else c+=a}}}},{key:"codespan",value:function(e){var t=this.rules.inline.code.exec(e);if(t){var n=t[2].replace(/\n/g," "),r=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return r&&i&&(n=n.substring(1,n.length-1)),n=v(n,!0),{type:"codespan",raw:t[0],text:n}}}},{key:"br",value:function(e){var t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}},{key:"del",value:function(e){var t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}},{key:"autolink",value:function(e){var t,n,r=this.rules.inline.autolink.exec(e);if(r)return"@"===r[2]?(t=v(r[1]),n="mailto:"+t):(t=v(r[1]),n=t),{type:"link",raw:r[0],text:t,href:n,tokens:[{type:"text",raw:t,text:t}]}}},{key:"url",value:function(e){var t;if(t=this.rules.inline.url.exec(e)){var n,r;if("@"===t[2])n=v(t[0]),r="mailto:"+n;else{var i;do{var u,a;i=t[0],t[0]=null!==(u=null===(a=this.rules.inline._backpedal.exec(t[0]))||void 0===a?void 0:a[0])&&void 0!==u?u:""}while(i!==t[0]);n=v(t[0]),r="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}},{key:"inlineText",value:function(e){var t,n=this.rules.inline.text.exec(e);if(n)return t=this.lexer.state.inRawBlock?n[0]:v(n[0]),{type:"text",raw:n[0],text:t}}}]),t}(),E=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,F=/(?:[*+-]|\d{1,9}[.)])/,A=b(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,F).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),B=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,_=/(?!\s*\])(?:\\.|[^\[\]\\])+/,z=b(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",_).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),S=b(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,F).getRegex(),M="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",T=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,$=b("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",T).replace("tag",M).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),R=b(B).replace("hr",E).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M).getRegex(),O=b(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",R).getRegex(),j={blockquote:O,code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:z,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:E,html:$,lheading:A,list:S,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:R,table:m,text:/^[^\n]+/},I=b("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",E).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M).getRegex(),L=a(a({},j),{},{table:I,paragraph:b(B).replace("hr",E).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",I).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",M).getRegex()}),P=a(a({},j),{},{html:b("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",T).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:m,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:b(B).replace("hr",E).replace("heading"," *#{1,6} *[^\n]").replace("lheading",A).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()}),W=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,H=/^( {2,}|\\)\n(?!\s*$)/,N=b(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,"\\p{P}\\p{S}").getRegex(),X=b(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,"\\p{P}\\p{S}").getRegex(),q=b("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,"\\p{P}\\p{S}").getRegex(),Y=b("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,"\\p{P}\\p{S}").getRegex(),Z=b(/\\([punct])/,"gu").replace(/punct/g,"\\p{P}\\p{S}").getRegex(),Q=b(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),U=b(T).replace("(?:--\x3e|$)","--\x3e").getRegex(),G=b("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",U).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),J=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,K=b(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",J).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),V=b(/^!?\[(label)\]\[(ref)\]/).replace("label",J).replace("ref",_).getRegex(),ee=b(/^!?\[(ref)\](?:\[\])?/).replace("ref",_).getRegex(),te=b("reflink|nolink(?!\\()","g").replace("reflink",V).replace("nolink",ee).getRegex(),ne={_backpedal:m,anyPunctuation:Z,autolink:Q,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:H,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:m,emStrongLDelim:X,emStrongRDelimAst:q,emStrongRDelimUnd:Y,escape:W,link:K,nolink:ee,punctuation:N,reflink:V,reflinkSearch:te,tag:G,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:m},re=a(a({},ne),{},{link:b(/^!?\[(label)\]\((.*?)\)/).replace("label",J).getRegex(),reflink:b(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",J).getRegex()}),ie=a(a({},ne),{},{escape:b(W).replace("])","~|])").getRegex(),url:b(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),ue=a(a({},ie),{},{br:b(H).replace("{2,}","*").getRegex(),text:b(ie.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),ae={normal:j,gfm:L,pedantic:P},oe={normal:ne,gfm:ie,breaks:ue,pedantic:re},se=function(){function t(n){l(this,t),h(this,"tokens",void 0),h(this,"options",void 0),h(this,"state",void 0),h(this,"tokenizer",void 0),h(this,"inlineQueue",void 0),this.tokens=[],this.tokens.links=Object.create(null),this.options=n||e.defaults,this.options.tokenizer=this.options.tokenizer||new y,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};var r={block:ae.normal,inline:oe.normal};this.options.pedantic?(r.block=ae.pedantic,r.inline=oe.pedantic):this.options.gfm&&(r.block=ae.gfm,this.options.breaks?r.inline=oe.breaks:r.inline=oe.gfm),this.tokenizer.rules=r}return c(t,[{key:"lex",value:function(e){e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);for(var t=0;t<this.inlineQueue.length;t++){var n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}},{key:"blockTokens",value:function(e){var t,n,r,i=this,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.options.pedantic&&(e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""));while(e){var o;if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((function(n){return!!(t=n.call({lexer:i},e,u))&&(e=e.substring(t.raw.length),u.push(t),!0)}))))if(t=this.tokenizer.space(e))e=e.substring(t.raw.length),1===t.raw.length&&u.length>0?u[u.length-1].raw+="\n":u.push(t);else if(t=this.tokenizer.code(e))e=e.substring(t.raw.length),n=u[u.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?u.push(t):(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.fences(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.heading(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.hr(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.blockquote(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.list(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.html(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.def(e))e=e.substring(t.raw.length),n=u[u.length-1],!n||"paragraph"!==n.type&&"text"!==n.type?this.tokens.links[t.tag]||(this.tokens.links[t.tag]={href:t.href,title:t.title}):(n.raw+="\n"+t.raw,n.text+="\n"+t.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(t=this.tokenizer.table(e))e=e.substring(t.raw.length),u.push(t);else if(t=this.tokenizer.lheading(e))e=e.substring(t.raw.length),u.push(t);else if(r=e,this.options.extensions&&this.options.extensions.startBlock&&function(){var t=1/0,n=e.slice(1),u=void 0;i.options.extensions.startBlock.forEach((function(e){u=e.call({lexer:i},n),"number"===typeof u&&u>=0&&(t=Math.min(t,u))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}(),this.state.top&&(t=this.tokenizer.paragraph(r)))n=u[u.length-1],a&&"paragraph"===(null===(o=n)||void 0===o?void 0:o.type)?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):u.push(t),a=r.length!==e.length,e=e.substring(t.raw.length);else if(t=this.tokenizer.text(e))e=e.substring(t.raw.length),n=u[u.length-1],n&&"text"===n.type?(n.raw+="\n"+t.raw,n.text+="\n"+t.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):u.push(t);else if(e){var s="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(s);break}throw new Error(s)}}return this.state.top=!0,u}},{key:"inline",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.inlineQueue.push({src:e,tokens:t}),t}},{key:"inlineTokens",value:function(e){var t,n,r,i,u,a,o=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],l=e;if(this.tokens.links){var c=Object.keys(this.tokens.links);if(c.length>0)while(null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(l)))c.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}while(null!=(i=this.tokenizer.rules.inline.blockSkip.exec(l)))l=l.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);while(null!=(i=this.tokenizer.rules.inline.anyPunctuation.exec(l)))l=l.slice(0,i.index)+"++"+l.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);while(e)if(u||(a=""),u=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((function(n){return!!(t=n.call({lexer:o},e,s))&&(e=e.substring(t.raw.length),s.push(t),!0)}))))if(t=this.tokenizer.escape(e))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.tag(e))e=e.substring(t.raw.length),n=s[s.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):s.push(t);else if(t=this.tokenizer.link(e))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(t.raw.length),n=s[s.length-1],n&&"text"===t.type&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):s.push(t);else if(t=this.tokenizer.emStrong(e,l,a))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.codespan(e))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.br(e))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.del(e))e=e.substring(t.raw.length),s.push(t);else if(t=this.tokenizer.autolink(e))e=e.substring(t.raw.length),s.push(t);else if(this.state.inLink||!(t=this.tokenizer.url(e))){if(r=e,this.options.extensions&&this.options.extensions.startInline&&function(){var t=1/0,n=e.slice(1),i=void 0;o.options.extensions.startInline.forEach((function(e){i=e.call({lexer:o},n),"number"===typeof i&&i>=0&&(t=Math.min(t,i))})),t<1/0&&t>=0&&(r=e.substring(0,t+1))}(),t=this.tokenizer.inlineText(r))e=e.substring(t.raw.length),"_"!==t.raw.slice(-1)&&(a=t.raw.slice(-1)),u=!0,n=s[s.length-1],n&&"text"===n.type?(n.raw+=t.raw,n.text+=t.text):s.push(t);else if(e){var h="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(h);break}throw new Error(h)}}else e=e.substring(t.raw.length),s.push(t);return s}}],[{key:"rules",get:function(){return{block:ae,inline:oe}}},{key:"lex",value:function(e,n){var r=new t(n);return r.lex(e)}},{key:"lexInline",value:function(e,n){var r=new t(n);return r.inlineTokens(e)}}]),t}(),le=function(){function t(n){l(this,t),h(this,"options",void 0),h(this,"parser",void 0),this.options=n||e.defaults}return c(t,[{key:"space",value:function(e){return""}},{key:"code",value:function(e){var t,n=e.text,r=e.lang,i=e.escaped,u=null===(t=(r||"").match(/^\S*/))||void 0===t?void 0:t[0],a=n.replace(/\n$/,"")+"\n";return u?'<pre><code class="language-'+v(u)+'">'+(i?a:v(a,!0))+"</code></pre>\n":"<pre><code>"+(i?a:v(a,!0))+"</code></pre>\n"}},{key:"blockquote",value:function(e){var t=e.tokens,n=this.parser.parse(t);return"<blockquote>\n".concat(n,"</blockquote>\n")}},{key:"html",value:function(e){var t=e.text;return t}},{key:"heading",value:function(e){var t=e.tokens,n=e.depth;return"<h".concat(n,">").concat(this.parser.parseInline(t),"</h").concat(n,">\n")}},{key:"hr",value:function(e){return"<hr>\n"}},{key:"list",value:function(e){for(var t=e.ordered,n=e.start,r="",i=0;i<e.items.length;i++){var u=e.items[i];r+=this.listitem(u)}var a=t?"ol":"ul",o=t&&1!==n?' start="'+n+'"':"";return"<"+a+o+">\n"+r+"</"+a+">\n"}},{key:"listitem",value:function(e){var t="";if(e.task){var n=this.checkbox({checked:!!e.checked});e.loose?e.tokens.length>0&&"paragraph"===e.tokens[0].type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+e.tokens[0].tokens[0].text)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" "}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),"<li>".concat(t,"</li>\n")}},{key:"checkbox",value:function(e){var t=e.checked;return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}},{key:"paragraph",value:function(e){var t=e.tokens;return"<p>".concat(this.parser.parseInline(t),"</p>\n")}},{key:"table",value:function(e){for(var t="",n="",r=0;r<e.header.length;r++)n+=this.tablecell(e.header[r]);t+=this.tablerow({text:n});for(var i="",u=0;u<e.rows.length;u++){var a=e.rows[u];n="";for(var o=0;o<a.length;o++)n+=this.tablecell(a[o]);i+=this.tablerow({text:n})}return i&&(i="<tbody>".concat(i,"</tbody>")),"<table>\n<thead>\n"+t+"</thead>\n"+i+"</table>\n"}},{key:"tablerow",value:function(e){var t=e.text;return"<tr>\n".concat(t,"</tr>\n")}},{key:"tablecell",value:function(e){var t=this.parser.parseInline(e.tokens),n=e.header?"th":"td",r=e.align?"<".concat(n,' align="').concat(e.align,'">'):"<".concat(n,">");return r+t+"</".concat(n,">\n")}},{key:"strong",value:function(e){var t=e.tokens;return"<strong>".concat(this.parser.parseInline(t),"</strong>")}},{key:"em",value:function(e){var t=e.tokens;return"<em>".concat(this.parser.parseInline(t),"</em>")}},{key:"codespan",value:function(e){var t=e.text;return"<code>".concat(t,"</code>")}},{key:"br",value:function(e){return"<br>"}},{key:"del",value:function(e){var t=e.tokens;return"<del>".concat(this.parser.parseInline(t),"</del>")}},{key:"link",value:function(e){var t=e.href,n=e.title,r=e.tokens,i=this.parser.parseInline(r),u=x(t);if(null===u)return i;t=u;var a='<a href="'+t+'"';return n&&(a+=' title="'+n+'"'),a+=">"+i+"</a>",a}},{key:"image",value:function(e){var t=e.href,n=e.title,r=e.text,i=x(t);if(null===i)return r;t=i;var u='<img src="'.concat(t,'" alt="').concat(r,'"');return n&&(u+=' title="'.concat(n,'"')),u+=">",u}},{key:"text",value:function(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):e.text}}]),t}(),ce=function(){function e(){l(this,e)}return c(e,[{key:"strong",value:function(e){var t=e.text;return t}},{key:"em",value:function(e){var t=e.text;return t}},{key:"codespan",value:function(e){var t=e.text;return t}},{key:"del",value:function(e){var t=e.text;return t}},{key:"html",value:function(e){var t=e.text;return t}},{key:"text",value:function(e){var t=e.text;return t}},{key:"link",value:function(e){var t=e.text;return""+t}},{key:"image",value:function(e){var t=e.text;return""+t}},{key:"br",value:function(){return""}}]),e}(),he=function(){function t(n){l(this,t),h(this,"options",void 0),h(this,"renderer",void 0),h(this,"textRenderer",void 0),this.options=n||e.defaults,this.options.renderer=this.options.renderer||new le,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ce}return c(t,[{key:"parse",value:function(e){for(var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n="",r=0;r<e.length;r++){var i=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){var u=i,a=this.options.extensions.renderers[u.type].call({parser:this},u);if(!1!==a||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)){n+=a||"";continue}}var o=i;switch(o.type){case"space":n+=this.renderer.space(o);continue;case"hr":n+=this.renderer.hr(o);continue;case"heading":n+=this.renderer.heading(o);continue;case"code":n+=this.renderer.code(o);continue;case"table":n+=this.renderer.table(o);continue;case"blockquote":n+=this.renderer.blockquote(o);continue;case"list":n+=this.renderer.list(o);continue;case"html":n+=this.renderer.html(o);continue;case"paragraph":n+=this.renderer.paragraph(o);continue;case"text":var s=o,l=this.renderer.text(s);while(r+1<e.length&&"text"===e[r+1].type)s=e[++r],l+="\n"+this.renderer.text(s);n+=t?this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l}]}):l;continue;default:var c='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(c),"";throw new Error(c)}}return n}},{key:"parseInline",value:function(e,t){t=t||this.renderer;for(var n="",r=0;r<e.length;r++){var i=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){var u=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==u||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=u||"";continue}}var a=i;switch(a.type){case"escape":n+=t.text(a);break;case"html":n+=t.html(a);break;case"link":n+=t.link(a);break;case"image":n+=t.image(a);break;case"strong":n+=t.strong(a);break;case"em":n+=t.em(a);break;case"codespan":n+=t.codespan(a);break;case"br":n+=t.br(a);break;case"del":n+=t.del(a);break;case"text":n+=t.text(a);break;default:var o='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}return n}}],[{key:"parse",value:function(e,n){var r=new t(n);return r.parse(e)}},{key:"parseInline",value:function(e,n){var r=new t(n);return r.parseInline(e)}}]),t}(),de=function(){function t(n){l(this,t),h(this,"options",void 0),h(this,"block",void 0),this.options=n||e.defaults}return c(t,[{key:"preprocess",value:function(e){return e}},{key:"postprocess",value:function(e){return e}},{key:"processAllTokens",value:function(e){return e}},{key:"provideLexer",value:function(){return this.block?se.lex:se.lexInline}},{key:"provideParser",value:function(){return this.block?he.parse:he.parseInline}}]),t}();h(de,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var fe=function(){function e(){l(this,e),h(this,"defaults",t()),h(this,"options",this.setOptions),h(this,"parse",this.parseMarkdown(!0)),h(this,"parseInline",this.parseMarkdown(!1)),h(this,"Parser",he),h(this,"Renderer",le),h(this,"TextRenderer",ce),h(this,"Lexer",se),h(this,"Tokenizer",y),h(this,"Hooks",de),this.use.apply(this,arguments)}return c(e,[{key:"walkTokens",value:function(e,t){var n,r=this,i=[],u=s(e);try{for(u.s();!(n=u.n()).done;){var a=n.value;switch(i=i.concat(t.call(this,a)),a.type){case"table":var o,l=a,c=s(l.header);try{for(c.s();!(o=c.n()).done;){var h=o.value;i=i.concat(this.walkTokens(h.tokens,t))}}catch(x){c.e(x)}finally{c.f()}var d,f=s(l.rows);try{for(f.s();!(d=f.n()).done;){var p,D=d.value,v=s(D);try{for(v.s();!(p=v.n()).done;){var g=p.value;i=i.concat(this.walkTokens(g.tokens,t))}}catch(x){v.e(x)}finally{v.f()}}}catch(x){f.e(x)}finally{f.f()}break;case"list":var b=a;i=i.concat(this.walkTokens(b.items,t));break;default:(function(){var e,n,u=a;null!==(e=r.defaults.extensions)&&void 0!==e&&null!==(n=e.childTokens)&&void 0!==n&&n[u.type]?r.defaults.extensions.childTokens[u.type].forEach((function(e){var n=u[e].flat(1/0);i=i.concat(r.walkTokens(n,t))})):u.tokens&&(i=i.concat(r.walkTokens(u.tokens,t)))})()}}}catch(x){u.e(x)}finally{u.f()}return i}},{key:"use",value:function(){for(var e=this,t=this.defaults.extensions||{renderers:{},childTokens:{}},n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return r.forEach((function(n){var r=a({},n);if(r.async=e.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach((function(e){if(!e.name)throw new Error("extension name required");if("renderer"in e){var n=t.renderers[e.name];t.renderers[e.name]=n?function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var u=e.renderer.apply(this,r);return!1===u&&(u=n.apply(this,r)),u}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");var r=t[e.level];r?r.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)})),r.extensions=t),n.renderer&&function(){var t=e.defaults.renderer||new le(e.defaults),i=function(e){if(!(e in t))throw new Error("renderer '".concat(e,"' does not exist"));if(["options","parser"].includes(e))return"continue";var r=e,i=n.renderer[r],u=t[r];t[r]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var a=i.apply(t,n);return!1===a&&(a=u.apply(t,n)),a||""}};for(var u in n.renderer)i(u);r.renderer=t}(),n.tokenizer&&function(){var t=e.defaults.tokenizer||new y(e.defaults),i=function(e){if(!(e in t))throw new Error("tokenizer '".concat(e,"' does not exist"));if(["options","rules","lexer"].includes(e))return"continue";var r=e,i=n.tokenizer[r],u=t[r];t[r]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var a=i.apply(t,n);return!1===a&&(a=u.apply(t,n)),a}};for(var u in n.tokenizer)i(u);r.tokenizer=t}(),n.hooks&&function(){var t=e.defaults.hooks||new de,i=function(r){if(!(r in t))throw new Error("hook '".concat(r,"' does not exist"));if(["options","block"].includes(r))return"continue";var i=r,u=n.hooks[i],a=t[i];de.passThroughHooks.has(r)?t[i]=function(n){if(e.defaults.async)return Promise.resolve(u.call(t,n)).then((function(e){return a.call(t,e)}));var r=u.call(t,n);return a.call(t,r)}:t[i]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=u.apply(t,n);return!1===i&&(i=a.apply(t,n)),i}};for(var u in n.hooks)i(u);r.hooks=t}(),n.walkTokens){var i=e.defaults.walkTokens,u=n.walkTokens;r.walkTokens=function(e){var t=[];return t.push(u.call(this,e)),i&&(t=t.concat(i.call(this,e))),t}}e.defaults=a(a({},e.defaults),r)})),this}},{key:"setOptions",value:function(e){return this.defaults=a(a({},this.defaults),e),this}},{key:"lexer",value:function(e,t){return se.lex(e,null!==t&&void 0!==t?t:this.defaults)}},{key:"parser",value:function(e,t){return he.parse(e,null!==t&&void 0!==t?t:this.defaults)}},{key:"parseMarkdown",value:function(e){var t=this;return function(n,r){var i=a({},r),u=a(a({},t.defaults),i),o=t.onError(!!u.silent,!!u.async);if(!0===t.defaults.async&&!1===i.async)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if("undefined"===typeof n||null===n)return o(new Error("marked(): input parameter is undefined or null"));if("string"!==typeof n)return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));u.hooks&&(u.hooks.options=u,u.hooks.block=e);var s=u.hooks?u.hooks.provideLexer():e?se.lex:se.lexInline,l=u.hooks?u.hooks.provideParser():e?he.parse:he.parseInline;if(u.async)return Promise.resolve(u.hooks?u.hooks.preprocess(n):n).then((function(e){return s(e,u)})).then((function(e){return u.hooks?u.hooks.processAllTokens(e):e})).then((function(e){return u.walkTokens?Promise.all(t.walkTokens(e,u.walkTokens)).then((function(){return e})):e})).then((function(e){return l(e,u)})).then((function(e){return u.hooks?u.hooks.postprocess(e):e})).catch(o);try{u.hooks&&(n=u.hooks.preprocess(n));var c=s(n,u);u.hooks&&(c=u.hooks.processAllTokens(c)),u.walkTokens&&t.walkTokens(c,u.walkTokens);var h=l(c,u);return u.hooks&&(h=u.hooks.postprocess(h)),h}catch(d){return o(d)}}}},{key:"onError",value:function(e,t){return function(n){if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){var r="<p>An error occurred:</p><pre>"+v(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}}}]),e}(),pe=new fe;function De(e,t){return pe.parse(e,t)}De.options=De.setOptions=function(e){return pe.setOptions(e),De.defaults=pe.defaults,n(De.defaults),De},De.getDefaults=t,De.defaults=e.defaults,De.use=function(){return pe.use.apply(pe,arguments),De.defaults=pe.defaults,n(De.defaults),De},De.walkTokens=function(e,t){return pe.walkTokens(e,t)},De.parseInline=pe.parseInline,De.Parser=he,De.parser=he.parse,De.Renderer=le,De.TextRenderer=ce,De.Lexer=se,De.lexer=se.lex,De.Tokenizer=y,De.Hooks=de,De.parse=De;var ve=De.options,ge=De.setOptions,be=De.use,xe=De.walkTokens,me=De.parseInline,ke=De,Ce=he.parse,we=se.lex;e.Hooks=de,e.Lexer=se,e.Marked=fe,e.Parser=he,e.Renderer=le,e.TextRenderer=ce,e.Tokenizer=y,e.getDefaults=t,e.lexer=we,e.marked=De,e.options=ve,e.parse=ke,e.parseInline=me,e.parser=Ce,e.setOptions=ge,e.use=be,e.walkTokens=xe}))}}]);