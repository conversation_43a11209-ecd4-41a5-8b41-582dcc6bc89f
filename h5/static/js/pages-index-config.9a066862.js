(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-config"],{"4ee6":function(n,e,t){"use strict";t.r(e);var u=t("d21c"),i=t.n(u);for(var r in u)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(r);e["default"]=i.a},9805:function(n,e,t){"use strict";t.r(e);var u=t("bcfb"),i=t("4ee6");for(var r in i)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(r);var c=t("828b"),f=Object(c["a"])(i["default"],u["b"],u["c"],!1,null,"e2ee93b2",null,!1,u["a"],void 0);e["default"]=f.exports},bcfb:function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){}));var u=function(){var n=this.$createElement,e=this._self._c||n;return e("v-uni-view")},i=[]},d21c:function(n,e,t){"use strict";t("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{}},methods:{}}}}]);